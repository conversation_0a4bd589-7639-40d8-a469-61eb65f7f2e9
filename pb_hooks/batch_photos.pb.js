// 批量照片记录保存 API

routerAdd("POST", "/api/photos/batch", (e) => {
  console.log('=== 收到批量照片保存请求 ===');
  
  try {
    // 检查用户认证
    const authRecord = e.auth;
    if (!authRecord) {
      console.log('用户未认证');
      return e.json(401, { 
        success: false, 
        error: '用户未认证' 
      });
    }
    
    console.log('用户已认证:', authRecord.id);
    
    // 使用 PocketBase 标准方式获取请求数据
    const data = $apis.requestInfo(e).data;
    console.log('请求数据:', JSON.stringify(data));
    
    if (!data) {
      return e.json(400, { 
        success: false, 
        error: '请求数据为空' 
      });
    }
    
    const spotId = data.spotId;
    const photos = data.photos;
    
    console.log('钓点ID:', spotId);
    console.log('照片数量:', photos ? photos.length : 0);
    
    if (!spotId || !photos || !Array.isArray(photos)) {
      return e.json(400, { 
        success: false, 
        error: '请求参数无效',
        received: { spotId, photos: photos ? 'array' : typeof photos }
      });
    }
    
    // 验证钓点是否存在
    try {
      const spot = $app.dao().findRecordById('fishing_spots', spotId);
      if (!spot) {
        return e.json(404, { 
          success: false, 
          error: '钓点不存在' 
        });
      }
      console.log('钓点验证成功:', spot.get('name'));
    } catch (spotError) {
      console.log('钓点验证失败:', spotError);
      return e.json(404, { 
        success: false, 
        error: '钓点不存在' 
      });
    }
    
    const savedPhotos = [];
    const errors = [];
    
    // 批量保存照片记录
    for (let i = 0; i < photos.length; i++) {
      const photo = photos[i];
      
      try {
        console.log(`处理照片 ${i + 1}:`, photo.filename);
        
        // 验证照片数据
        if (!photo.filename || !photo.url) {
          errors.push(`照片 ${i + 1}: 缺少必要字段`);
          continue;
        }
        
        // 创建照片记录
        const collection = $app.dao().findCollectionByNameOrId('spot_photos');
        const record = new Record(collection);
        
        // 设置字段
        record.set('spot_id', spotId);
        record.set('user_id', authRecord.id);
        record.set('filename', photo.filename);
        record.set('url', photo.url);
        record.set('thumbnail_url', photo.thumbnailUrl || photo.url);
        record.set('storage_path', photo.storagePath || photo.url);
        record.set('thumbnail_path', photo.thumbnailPath || photo.storagePath || photo.url);
        record.set('type', photo.type || 'normal');
        record.set('sort_order', photo.sortOrder || i);
        record.set('file_size', photo.fileSize || 0);
        record.set('mime_type', photo.mimeType || 'image/jpeg');
        
        // 保存记录
        $app.dao().saveRecord(record);
        
        savedPhotos.push({
          id: record.id,
          filename: photo.filename,
          url: photo.url
        });
        
        console.log(`照片记录保存成功: ${photo.filename} -> ${record.id}`);
        
      } catch (photoError) {
        console.log(`保存照片 ${i + 1} 失败:`, photoError);
        errors.push(`照片 ${i + 1}: ${photoError.message || '保存失败'}`);
      }
    }
    
    // 返回结果
    const result = {
      success: true,
      savedCount: savedPhotos.length,
      totalCount: photos.length,
      savedPhotos: savedPhotos
    };
    
    if (errors.length > 0) {
      result.errors = errors;
      result.hasErrors = true;
    }
    
    console.log(`批量照片保存完成: ${savedPhotos.length}/${photos.length} 成功`);
    
    return e.json(200, result);
    
  } catch (error) {
    console.error('批量照片保存失败:', error);
    return e.json(500, { 
      success: false,
      error: error.message || '服务器内部错误' 
    });
  }
});

console.log('📸 批量照片保存 API 已加载');
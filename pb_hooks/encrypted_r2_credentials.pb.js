// 加密的 R2 凭据分发系统 - 完全自包含版本

routerAdd("GET", "/api/get-encrypted-r2-credentials", (e) => {
  console.log('收到加密R2凭据请求');
  
  try {
    // 简单的 XOR 加密函数
    function simpleEncrypt(text, key) {
      let result = '';
      for (let i = 0; i < text.length; i++) {
        result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
      }
      // 手动实现 Base64 编码（PocketBase 不支持 btoa）
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
      let base64 = '';
      for (let i = 0; i < result.length; i += 3) {
        const a = result.charCodeAt(i);
        const b = i + 1 < result.length ? result.charCodeAt(i + 1) : 0;
        const c = i + 2 < result.length ? result.charCodeAt(i + 2) : 0;
        
        const bitmap = (a << 16) | (b << 8) | c;
        
        base64 += chars.charAt((bitmap >> 18) & 63);
        base64 += chars.charAt((bitmap >> 12) & 63);
        base64 += i + 1 < result.length ? chars.charAt((bitmap >> 6) & 63) : '=';
        base64 += i + 2 < result.length ? chars.charAt(bitmap & 63) : '=';
      }
      return base64;
    }
    
    const authRecord = e.auth;
    
    // 确定权限级别
    let permissionLevel = 'readonly'; // 默认只读
    
    if (authRecord) {
      // 用户已登录，给予读写权限
      permissionLevel = 'readwrite';
      
      // 检查是否是管理员
      try {
        const userRole = authRecord.get('role');
        const isAdmin = authRecord.get('is_admin');
        if (userRole === 'admin' || isAdmin === true) {
          permissionLevel = 'admin';
        }
      } catch (roleError) {
        console.log('获取用户角色失败，使用默认读写权限:', roleError);
      }
    }
    
    console.log('用户权限级别:', permissionLevel);
    console.log('用户ID:', authRecord ? authRecord.id : 'anonymous');
    
    // R2 凭据配置
    const allCredentials = {
      readonly: {
        accessKeyId: '9ce7c7d0cde4eedeee735ddc2dc6a225',
        secretAccessKey: '****************************************************************',
        permissions: ['read']
      },
      readwrite: {
        accessKeyId: '2975b9f0b5ed91f29bec884c3fdcd4c8',
        secretAccessKey: '****************************************************************', 
        permissions: ['read', 'write']
      },
      admin: {
        accessKeyId: 'ade7e701765b4ea99c1fabac282b56cc',
        secretAccessKey: '****************************************************************',
        permissions: ['read', 'write', 'delete']
      }
    };
    
    const credentials = allCredentials[permissionLevel];
    if (!credentials) {
      return e.json(400, { error: '无效的权限级别' });
    }
    
    // 加密密钥
    const encryptionKey = 'fishing-app-secret-key-2025';
    
    // 加密凭据
    const encryptedCredentials = {
      accessKeyId: simpleEncrypt(credentials.accessKeyId, encryptionKey),
      secretAccessKey: simpleEncrypt(credentials.secretAccessKey, encryptionKey),
      permissions: credentials.permissions
    };
    
    // 基础配置
    const baseConfig = {
      endpoint: 'https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com',
      bucketName: 'fishing-app',
      region: 'auto'
    };
    
    // 返回加密的凭据和基础配置
    return e.json(200, {
      success: true,
      permissionLevel: permissionLevel,
      encryptedCredentials: encryptedCredentials,
      baseConfig: baseConfig,
      expiresIn: 3600,
      timestamp: Date.now(),
      note: '凭据已加密，需要在客户端解密后使用'
    });
    
  } catch (error) {
    console.error('获取加密R2凭据失败:', error);
    return e.json(500, { 
      success: false,
      error: error.message || '服务器内部错误' 
    });
  }
});

console.log('🔐 加密R2凭据分发系统已加载');
// 同步点赞数脚本
// 用于将现有的点赞数据同步到钓点表的冗余字段中

routerAdd("POST", "/api/sync-likes-count", (c) => {
  try {
    let syncedCount = 0;
    let errorCount = 0;
    const results = [];
    
    // 获取所有钓点
    const fishingSpots = $app.findRecordsByFilter("fishing_spots", "", "-created", 0);
    
    fishingSpots.forEach(spot => {
      try {
        // 计算该钓点的实际点赞数
        const likeRecords = $app.findRecordsByFilter(
          "spot_likes", 
          `spot_id = "${spot.id}" && is_like = true`, 
          "", 
          0
        );
        
        const actualLikesCount = likeRecords ? likeRecords.length : 0;
        
        // 更新钓点的点赞数字段
        spot.set("likes_count", actualLikesCount);
        spot.set("likes_count_updated_at", new Date().toISOString());
        
        $app.save(spot);
        
        results.push({
          spotId: spot.id,
          spotName: spot.get("name"),
          likesCount: actualLikesCount,
          status: "success"
        });
        
        syncedCount++;
        
      } catch (e) {
        results.push({
          spotId: spot.id,
          spotName: spot.get("name"),
          error: e.message,
          status: "error"
        });
        errorCount++;
      }
    });
    
    return c.json(200, {
      success: true,
      message: "点赞数同步完成",
      summary: {
        totalSpots: fishingSpots.length,
        syncedCount: syncedCount,
        errorCount: errorCount
      },
      details: results
    });
    
  } catch (error) {
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

// 验证同步结果
routerAdd("GET", "/api/verify-likes-sync", (c) => {
  try {
    const results = [];
    
    // 获取所有钓点
    const fishingSpots = $app.findRecordsByFilter("fishing_spots", "", "-created", 10);
    
    fishingSpots.forEach(spot => {
      // 计算实际点赞数
      const likeRecords = $app.findRecordsByFilter(
        "spot_likes", 
        `spot_id = "${spot.id}" && is_like = true`, 
        "", 
        0
      );
      
      const actualCount = likeRecords ? likeRecords.length : 0;
      const storedCount = spot.get("likes_count") || 0;
      
      results.push({
        spotId: spot.id,
        spotName: spot.get("name"),
        actualCount: actualCount,
        storedCount: storedCount,
        isConsistent: actualCount === storedCount
      });
    });
    
    const inconsistentCount = results.filter(r => !r.isConsistent).length;
    
    return c.json(200, {
      success: true,
      message: "同步验证完成",
      summary: {
        totalChecked: results.length,
        consistentCount: results.length - inconsistentCount,
        inconsistentCount: inconsistentCount
      },
      details: results
    });
    
  } catch (error) {
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

console.log('点赞数同步脚本已加载');
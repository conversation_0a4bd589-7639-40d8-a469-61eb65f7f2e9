// 修复 spot_likes 集合的字段定义
// 使用正确的 PocketBase GoJS API

routerAdd("POST", "/api/fix-spot-likes", (c) => {
  try {
    // 获取现有的 spot_likes 集合
    const collection = $app.findCollectionByNameOrId("spot_likes");
    
    if (!collection) {
      return c.json(404, {
        success: false,
        error: "spot_likes 集合不存在"
      });
    }
    
    // 重新定义字段 schema
    collection.schema = [
      {
        id: "user_id_field",
        name: "user_id",
        type: "relation",
        system: false,
        required: true,
        presentable: false,
        options: {
          collectionId: "_pb_users_auth_", // users 集合的 ID
          cascadeDelete: true,
          minSelect: null,
          maxSelect: 1,
          displayFields: ["username", "email"]
        }
      },
      {
        id: "spot_id_field", 
        name: "spot_id",
        type: "relation",
        system: false,
        required: true,
        presentable: false,
        options: {
          collectionId: "pbc_3400286026", // fishing_spots 集合的 ID
          cascadeDelete: true,
          minSelect: null,
          maxSelect: 1,
          displayFields: ["name"]
        }
      },
      {
        id: "is_like_field",
        name: "is_like",
        type: "bool",
        system: false,
        required: true,
        presentable: false,
        options: {}
      }
    ];
    
    // 修复权限规则
    collection.listRule = "@request.auth.id != \"\"";
    collection.viewRule = "@request.auth.id != \"\"";
    collection.createRule = "@request.auth.id != \"\" && @request.auth.id = user_id";
    collection.updateRule = "@request.auth.id != \"\" && @request.auth.id = user_id";
    collection.deleteRule = "@request.auth.id != \"\" && @request.auth.id = user_id";
    
    // 保存集合
    $app.save(collection);
    
    return c.json(200, {
      success: true,
      message: "spot_likes 集合字段和权限修复成功",
      collection: {
        id: collection.id,
        name: collection.name,
        fieldsCount: collection.schema ? collection.schema.length : 0,
        fields: collection.schema ? collection.schema.map(f => ({
          name: f.name,
          type: f.type,
          required: f.required
        })) : []
      }
    });
    
  } catch (error) {
    console.error('修复 spot_likes 集合失败:', error);
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

// 验证修复结果
routerAdd("GET", "/api/check-spot-likes-fix", (c) => {
  try {
    const collection = $app.findCollectionByNameOrId("spot_likes");
    
    return c.json(200, {
      success: true,
      collection: {
        id: collection.id,
        name: collection.name,
        type: collection.type,
        fieldsCount: collection.schema ? collection.schema.length : 0,
        fields: collection.schema ? collection.schema.map(f => ({
          name: f.name,
          type: f.type,
          required: f.required,
          options: f.options
        })) : [],
        permissions: {
          listRule: collection.listRule,
          viewRule: collection.viewRule,
          createRule: collection.createRule,
          updateRule: collection.updateRule,
          deleteRule: collection.deleteRule
        }
      }
    });
    
  } catch (error) {
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

console.log('spot_likes 修复脚本已加载');
// 查询PocketBase所有集合和属性的脚本
// 提供API接口来获取数据库结构信息

routerAdd("GET", "/api/list-collections", (c) => {
  try {
    // 使用简化的方法获取集合信息
    const result = {
      total: 0,
      collections: []
    };
    
    // 尝试获取已知的集合
    const knownCollections = ["users", "fishing_spots", "spot_photos", "spot_likes", "user_follows", "user_favorites"];
    
    knownCollections.forEach(collectionName => {
      try {
        const collection = $app.dao().findCollectionByNameOrId(collectionName);
        if (collection) {
          const collectionInfo = {
            id: collection.id,
            name: collection.name,
            type: collection.type,
            created: collection.created,
            updated: collection.updated,
            system: collection.system,
            listRule: collection.listRule,
            viewRule: collection.viewRule,
            createRule: collection.createRule,
            updateRule: collection.updateRule,
            deleteRule: collection.deleteRule,
            schema: []
          };
          
          // 获取字段信息
          if (collection.schema && collection.schema.length > 0) {
            collection.schema.forEach(field => {
              collectionInfo.schema.push({
                id: field.id,
                name: field.name,
                type: field.type,
                system: field.system,
                required: field.required,
                presentable: field.presentable,
                options: field.options || {}
              });
            });
          }
          
          result.collections.push(collectionInfo);
          result.total++;
        }
      } catch (e) {
        // 集合不存在，跳过
      }
    });
    
    return c.json(200, {
      success: true,
      message: "成功获取所有集合信息",
      data: result,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('获取集合信息失败:', error);
    return c.json(500, {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 查询特定集合的详细信息
routerAdd("GET", "/api/collection-info/:name", (c) => {
  try {
    const collectionName = c.pathParam("name");
    
    if (!collectionName) {
      return c.json(400, {
        success: false,
        error: "集合名称不能为空"
      });
    }
    
    const collection = $app.dao.findCollectionByNameOrId(collectionName);
    
    if (!collection) {
      return c.json(404, {
        success: false,
        error: "集合不存在: " + collectionName
      });
    }
    
    const collectionInfo = {
      id: collection.id,
      name: collection.name,
      type: collection.type,
      created: collection.created,
      updated: collection.updated,
      system: collection.system,
      listRule: collection.listRule,
      viewRule: collection.viewRule,
      createRule: collection.createRule,
      updateRule: collection.updateRule,
      deleteRule: collection.deleteRule,
      schema: []
    };
    
    // 获取字段详细信息
    if (collection.schema && collection.schema.length > 0) {
      collection.schema.forEach(field => {
        const fieldInfo = {
          id: field.id,
          name: field.name,
          type: field.type,
          system: field.system,
          required: field.required,
          presentable: field.presentable,
          options: field.options || {}
        };
        
        // 关联字段的额外信息
        if (field.type === "relation" && field.options && field.options.collectionId) {
          try {
            const relatedCollection = $app.dao.findCollectionByNameOrId(field.options.collectionId);
            fieldInfo.relatedCollection = {
              id: relatedCollection.id,
              name: relatedCollection.name
            };
          } catch (e) {
            fieldInfo.relatedCollection = {
              error: "Collection not found: " + field.options.collectionId
            };
          }
        }
        
        collectionInfo.schema.push(fieldInfo);
      });
    }
    
    // 获取记录数量
    try {
      const recordsCount = $app.dao.db()
        .select("count(*)")
        .from(collection.name)
        .build()
        .execute();
      
      collectionInfo.recordsCount = recordsCount[0]["count(*)"] || 0;
    } catch (e) {
      collectionInfo.recordsCount = "Error: " + e.message;
    }
    
    // 获取最近的几条记录作为示例
    try {
      const sampleRecords = $app.dao.db()
        .select("*")
        .from(collection.name)
        .limit(3)
        .orderBy("created DESC")
        .build()
        .execute();
      
      collectionInfo.sampleRecords = sampleRecords;
    } catch (e) {
      collectionInfo.sampleRecords = [];
    }
    
    return c.json(200, {
      success: true,
      message: "成功获取集合信息: " + collectionName,
      data: collectionInfo,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('获取集合信息失败:', error);
    return c.json(500, {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 检查集合是否存在
routerAdd("GET", "/api/check-collection/:name", (c) => {
  try {
    const collectionName = c.pathParam("name");
    
    if (!collectionName) {
      return c.json(400, {
        success: false,
        error: "集合名称不能为空"
      });
    }
    
    try {
      const collection = $app.dao.findCollectionByNameOrId(collectionName);
      return c.json(200, {
        success: true,
        exists: true,
        collection: {
          id: collection.id,
          name: collection.name,
          type: collection.type
        }
      });
    } catch (e) {
      return c.json(200, {
        success: true,
        exists: false,
        message: "集合不存在: " + collectionName
      });
    }
    
  } catch (error) {
    console.error('检查集合失败:', error);
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

console.log('集合查询脚本已加载 - 可用接口:');
console.log('  GET /api/list-collections - 获取所有集合');
console.log('  GET /api/collection-info/:name - 获取特定集合信息');
console.log('  GET /api/check-collection/:name - 检查集合是否存在');
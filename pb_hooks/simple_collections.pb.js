// 简化的PocketBase集合查询脚本

routerAdd("GET", "/api/simple-collections", (c) => {
  try {
    const result = {
      collections: [],
      timestamp: new Date().toISOString()
    };
    
    // 检查已知集合
    const knownCollections = ["users", "fishing_spots", "spot_photos", "spot_likes", "user_follows", "user_favorites"];
    
    knownCollections.forEach(name => {
      try {
        const collection = $app.findCollectionByNameOrId(name);
        result.collections.push({
          name: name,
          exists: true,
          id: collection.id,
          type: collection.type,
          fieldsCount: collection.schema ? collection.schema.length : 0,
          fields: collection.schema ? collection.schema.map(f => ({
            name: f.name,
            type: f.type,
            required: f.required
          })) : []
        });
      } catch (e) {
        result.collections.push({
          name: name,
          exists: false,
          error: e.message
        });
      }
    });
    
    return c.json(200, {
      success: true,
      data: result
    });
    
  } catch (error) {
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

// 检查特定集合 - 使用不同的路由避免冲突
routerAdd("GET", "/api/simple-check/:name", (c) => {
  try {
    const name = c.pathParam("name");
    
    try {
      const collection = $app.findCollectionByNameOrId(name);
      return c.json(200, {
        success: true,
        exists: true,
        collection: {
          id: collection.id,
          name: collection.name,
          type: collection.type,
          fields: collection.schema ? collection.schema.map(f => ({
            name: f.name,
            type: f.type,
            required: f.required
          })) : []
        }
      });
    } catch (e) {
      return c.json(200, {
        success: true,
        exists: false,
        message: "集合不存在: " + name
      });
    }
    
  } catch (error) {
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

console.log('简化集合查询脚本已加载');
// 通用集合字段验证脚本
// 用于详细检查所有集合的字段定义、权限和数据

routerAdd("GET", "/api/verify-collections", (c) => {
  try {
    const result = {
      collections: [],
      timestamp: new Date().toISOString(),
      summary: {
        total: 0,
        withFields: 0,
        withoutFields: 0
      }
    };
    
    // 检查所有已知集合
    const knownCollections = ["users", "fishing_spots", "spot_photos", "spot_likes", "user_follows", "user_favorites"];
    
    knownCollections.forEach(name => {
      try {
        const collection = $app.findCollectionByNameOrId(name);
        
        const collectionInfo = {
          name: name,
          exists: true,
          id: collection.id,
          type: collection.type,
          created: collection.created,
          updated: collection.updated,
          system: collection.system || false,
          
          // 权限规则
          permissions: {
            listRule: collection.listRule || "null",
            viewRule: collection.viewRule || "null", 
            createRule: collection.createRule || "null",
            updateRule: collection.updateRule || "null",
            deleteRule: collection.deleteRule || "null"
          },
          
          // 字段详细信息
          schema: {
            fieldsCount: 0,
            fields: []
          }
        };
        
        // 详细解析字段信息
        if (collection.schema) {
          collectionInfo.schema.fieldsCount = collection.schema.length;
          
          collection.schema.forEach(field => {
            const fieldInfo = {
              id: field.id || "unknown",
              name: field.name || "unknown",
              type: field.type || "unknown",
              system: field.system || false,
              required: field.required || false,
              presentable: field.presentable || false
            };
            
            // 添加选项信息
            if (field.options) {
              fieldInfo.options = {};
              
              // 根据字段类型添加相关选项
              switch (field.type) {
                case "relation":
                  fieldInfo.options.collectionId = field.options.collectionId || "unknown";
                  fieldInfo.options.cascadeDelete = field.options.cascadeDelete || false;
                  fieldInfo.options.minSelect = field.options.minSelect;
                  fieldInfo.options.maxSelect = field.options.maxSelect;
                  fieldInfo.options.displayFields = field.options.displayFields || [];
                  break;
                case "select":
                  fieldInfo.options.maxSelect = field.options.maxSelect;
                  fieldInfo.options.values = field.options.values || [];
                  break;
                case "file":
                  fieldInfo.options.maxSelect = field.options.maxSelect;
                  fieldInfo.options.maxSize = field.options.maxSize;
                  fieldInfo.options.mimeTypes = field.options.mimeTypes || [];
                  break;
                case "number":
                  fieldInfo.options.min = field.options.min;
                  fieldInfo.options.max = field.options.max;
                  fieldInfo.options.noDecimal = field.options.noDecimal || false;
                  break;
                case "text":
                  fieldInfo.options.min = field.options.min;
                  fieldInfo.options.max = field.options.max;
                  fieldInfo.options.pattern = field.options.pattern || "";
                  break;
                case "bool":
                  // bool 类型通常没有特殊选项
                  break;
                default:
                  fieldInfo.options = field.options;
              }
            }
            
            collectionInfo.schema.fields.push(fieldInfo);
          });
          
          result.summary.withFields++;
        } else {
          result.summary.withoutFields++;
        }
        
        // 尝试获取记录数量（安全方式）
        try {
          // 使用简单的查询来测试集合访问权限
          const testQuery = $app.findRecordsByFilter(name, "", "-created", 1);
          collectionInfo.recordsCount = "accessible";
          collectionInfo.hasRecords = testQuery && testQuery.length > 0;
        } catch (e) {
          collectionInfo.recordsCount = "access_denied";
          collectionInfo.accessError = e.message;
        }
        
        result.collections.push(collectionInfo);
        result.summary.total++;
        
      } catch (e) {
        result.collections.push({
          name: name,
          exists: false,
          error: e.message
        });
      }
    });
    
    return c.json(200, {
      success: true,
      message: "集合验证完成",
      data: result
    });
    
  } catch (error) {
    return c.json(500, {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 专门验证 spot_likes 集合的详细信息
routerAdd("GET", "/api/verify-spot-likes", (c) => {
  try {
    const collection = $app.findCollectionByNameOrId("spot_likes");
    
    const result = {
      collection: {
        name: "spot_likes",
        id: collection.id,
        type: collection.type,
        created: collection.created,
        updated: collection.updated,
        
        // 详细权限信息
        permissions: {
          listRule: collection.listRule,
          viewRule: collection.viewRule,
          createRule: collection.createRule,
          updateRule: collection.updateRule,
          deleteRule: collection.deleteRule
        },
        
        // 完整字段信息
        fields: []
      },
      
      // 测试数据访问
      dataAccess: {
        canList: false,
        canCreate: false,
        errorMessage: null
      }
    };
    
    // 解析所有字段
    if (collection.schema) {
      collection.schema.forEach(field => {
        result.collection.fields.push({
          id: field.id,
          name: field.name,
          type: field.type,
          system: field.system,
          required: field.required,
          presentable: field.presentable,
          options: field.options || {}
        });
      });
    }
    
    // 测试数据访问权限
    try {
      const records = $app.findRecordsByFilter("spot_likes", "", "-created", 1);
      result.dataAccess.canList = true;
      result.dataAccess.recordCount = records ? records.length : 0;
    } catch (e) {
      result.dataAccess.canList = false;
      result.dataAccess.errorMessage = e.message;
    }
    
    // 分析字段是否符合预期
    const expectedFields = ["user_id", "spot_id", "is_like"];
    result.analysis = {
      expectedFields: expectedFields,
      actualFields: result.collection.fields.map(f => f.name),
      missingFields: [],
      extraFields: []
    };
    
    expectedFields.forEach(expected => {
      if (!result.collection.fields.find(f => f.name === expected)) {
        result.analysis.missingFields.push(expected);
      }
    });
    
    result.collection.fields.forEach(field => {
      if (!expectedFields.includes(field.name) && !field.system) {
        result.analysis.extraFields.push(field.name);
      }
    });
    
    return c.json(200, {
      success: true,
      message: "spot_likes 集合验证完成",
      data: result,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    return c.json(500, {
      success: false,
      error: error.message,
      message: "spot_likes 集合不存在或访问失败"
    });
  }
});

// 测试 spot_likes 的具体查询
routerAdd("GET", "/api/test-spot-likes-query", (c) => {
  try {
    const result = {
      tests: [],
      timestamp: new Date().toISOString()
    };
    
    // 测试1: 简单列表查询
    try {
      const records = $app.findRecordsByFilter("spot_likes", "", "-created", 5);
      result.tests.push({
        test: "simple_list",
        success: true,
        recordCount: records ? records.length : 0,
        message: "简单列表查询成功"
      });
      
      // 如果有记录，显示第一条的字段
      if (records && records.length > 0) {
        const firstRecord = records[0];
        result.tests.push({
          test: "sample_record",
          success: true,
          sampleFields: Object.keys(firstRecord.data || {}),
          message: "获取到示例记录字段"
        });
      }
    } catch (e) {
      result.tests.push({
        test: "simple_list",
        success: false,
        error: e.message
      });
    }
    
    // 测试2: 带过滤条件的查询（类似原始API）
    try {
      const records = $app.findRecordsByFilter("spot_likes", "created != ''", "-created", 1);
      result.tests.push({
        test: "filtered_query",
        success: true,
        recordCount: records ? records.length : 0,
        message: "过滤查询成功"
      });
    } catch (e) {
      result.tests.push({
        test: "filtered_query",
        success: false,
        error: e.message
      });
    }
    
    return c.json(200, {
      success: true,
      message: "spot_likes 查询测试完成",
      data: result
    });
    
  } catch (error) {
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

console.log('集合验证脚本已加载 - 可用接口:');
console.log('  GET /api/verify-collections - 验证所有集合');
console.log('  GET /api/verify-spot-likes - 专门验证 spot_likes');
console.log('  GET /api/test-spot-likes-query - 测试 spot_likes 查询');
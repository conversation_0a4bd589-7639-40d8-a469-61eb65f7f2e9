// 重新创建 spot_likes 集合（删除旧的，创建新的）
// 使用正确的 PocketBase GoJS API

routerAdd("POST", "/api/recreate-spot-likes", (c) => {
  try {
    // 先删除现有的 spot_likes 集合
    try {
      const existingCollection = $app.findCollectionByNameOrId("spot_likes");
      if (existingCollection) {
        $app.delete(existingCollection);
        console.log('已删除现有的 spot_likes 集合');
      }
    } catch (e) {
      console.log('删除现有集合时出错（可能不存在）:', e.message);
    }
    
    // 创建新的集合对象
    const newCollection = new Collection();
    newCollection.name = "spot_likes";
    newCollection.type = "base";
    
    // 设置权限规则
    newCollection.listRule = "@request.auth.id != \"\"";
    newCollection.viewRule = "@request.auth.id != \"\"";
    newCollection.createRule = "@request.auth.id != \"\" && @request.auth.id = user_id";
    newCollection.updateRule = "@request.auth.id != \"\" && @request.auth.id = user_id";
    newCollection.deleteRule = "@request.auth.id != \"\" && @request.auth.id = user_id";
    
    // 保存基础集合
    $app.save(newCollection);
    console.log('基础集合创建成功，ID:', newCollection.id);
    
    // 现在添加字段（使用 PocketBase 的字段添加方法）
    const userIdField = new SchemaField();
    userIdField.name = "user_id";
    userIdField.type = "relation";
    userIdField.required = true;
    userIdField.options = {
      collectionId: "_pb_users_auth_",
      cascadeDelete: true,
      minSelect: null,
      maxSelect: 1,
      displayFields: ["username", "email"]
    };
    
    const spotIdField = new SchemaField();
    spotIdField.name = "spot_id";
    spotIdField.type = "relation";
    spotIdField.required = true;
    spotIdField.options = {
      collectionId: "pbc_3400286026",
      cascadeDelete: true,
      minSelect: null,
      maxSelect: 1,
      displayFields: ["name"]
    };
    
    const isLikeField = new SchemaField();
    isLikeField.name = "is_like";
    isLikeField.type = "bool";
    isLikeField.required = true;
    isLikeField.options = {};
    
    // 添加字段到集合
    newCollection.schema = [userIdField, spotIdField, isLikeField];
    
    // 再次保存以应用字段
    $app.save(newCollection);
    
    return c.json(200, {
      success: true,
      message: "spot_likes 集合重新创建成功",
      collection: {
        id: newCollection.id,
        name: newCollection.name,
        fieldsCount: newCollection.schema ? newCollection.schema.length : 0,
        fields: newCollection.schema ? newCollection.schema.map(f => ({
          name: f.name,
          type: f.type,
          required: f.required
        })) : []
      }
    });
    
  } catch (error) {
    console.error('重新创建 spot_likes 集合失败:', error);
    return c.json(500, {
      success: false,
      error: error.message,
      details: error.toString()
    });
  }
});

// 简化版本：只修复权限规则
routerAdd("POST", "/api/fix-spot-likes-permissions", (c) => {
  try {
    const collection = $app.findCollectionByNameOrId("spot_likes");
    
    // 只修改权限规则
    collection.listRule = "@request.auth.id != \"\"";
    collection.viewRule = "@request.auth.id != \"\"";
    collection.createRule = "@request.auth.id != \"\"";
    
    $app.save(collection);
    
    return c.json(200, {
      success: true,
      message: "spot_likes 权限规则修复成功",
      permissions: {
        listRule: collection.listRule,
        viewRule: collection.viewRule,
        createRule: collection.createRule,
        updateRule: collection.updateRule,
        deleteRule: collection.deleteRule
      }
    });
    
  } catch (error) {
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

console.log('spot_likes 重新创建脚本已加载');
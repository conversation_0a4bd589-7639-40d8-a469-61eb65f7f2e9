import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/pocketbase_config.dart';
import '../models/user.dart';
import 'service_locator.dart';

/// 核心认证服务
///
/// 职责：
/// - 用户认证（登录、注册、登出）
/// - 会话管理和状态监听
/// - 自动登录功能
/// - 认证状态通知
/// - 权限检查
class AuthService {
  // 单例模式
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // 认证状态通知器
  final ValueNotifier<User?> _currentUserNotifier = ValueNotifier(null);
  ValueListenable<User?> get currentUserNotifier => _currentUserNotifier;

  // 缓存的登录凭据键
  static const String _emailKey = 'cached_email';
  static const String _passwordKey = 'cached_password';
  static const String _phoneKey = 'cached_phone';
  static const String _autoLoginKey = 'auto_login_enabled';

  /// 获取当前登录用户
  User? get currentUser => _currentUserNotifier.value;

  /// 检查用户是否已登录
  bool get isLoggedIn =>
      pb.authStore.isValid && _currentUserNotifier.value != null;

  /// 获取当前用户的认证令牌
  String get token => pb.authStore.token;

  /// 初始化认证服务
  Future<void> initialize() async {
    debugPrint('初始化认证服务');

    // 设置认证状态监听
    _setupAuthListener();

    // 检查是否启用了自动登录
    final prefs = await SharedPreferences.getInstance();
    final autoLoginEnabled = prefs.getBool(_autoLoginKey) ?? false;

    if (autoLoginEnabled && !isLoggedIn) {
      await _attemptAutoLogin();
    } else if (pb.authStore.isValid) {
      // 如果PocketBase已登录，获取用户信息
      await _fetchCurrentUserFromPocketBase();
    }
  }

  /// 设置认证状态监听
  void _setupAuthListener() {
    pb.authStore.onChange.listen((event) {
      final record = pb.authStore.record;
      debugPrint('PocketBase 认证状态变化: ${record != null ? "已登录" : "已登出"}');

      if (record != null) {
        // 用户登录时，获取用户信息
        _fetchCurrentUserFromPocketBase().catchError((e) {
          debugPrint('获取用户信息失败: $e');
        });

        // 清理钓点缓存，确保新用户看到正确的数据
        _clearRelatedCaches();
      } else {
        // 用户登出时，清除当前用户
        _currentUserNotifier.value = null;

        // 清理钓点缓存，确保数据安全
        _clearRelatedCaches();
      }
    });
  }

  /// 从PocketBase获取当前用户信息
  Future<void> _fetchCurrentUserFromPocketBase() async {
    try {
      final authRecord = pb.authStore.record;
      if (authRecord == null) return;

      final record = await pb.collection('users').getOne(authRecord.id);
      final user = User.fromJson(record.toJson());
      _currentUserNotifier.value = user;
      debugPrint('成功获取用户信息: ${user.username}');
    } catch (e) {
      debugPrint('获取用户信息失败: $e');
    }
  }

  /// 尝试自动登录
  Future<bool> _attemptAutoLogin() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final email = prefs.getString(_emailKey);
      final password = prefs.getString(_passwordKey);
      final phone = prefs.getString(_phoneKey);

      if (email != null && password != null) {
        debugPrint('尝试使用缓存的邮箱凭据自动登录: $email');
        await login(
          email: email,
          password: password,
          rememberCredentials: false,
        );
        debugPrint('自动登录成功');
        return true;
      } else if (phone != null) {
        debugPrint('尝试使用缓存的手机号凭据自动登录: $phone');
        await phoneLogin(phoneNumber: phone, rememberCredentials: false);
        debugPrint('自动登录成功');
        return true;
      } else {
        debugPrint('没有找到保存的登录凭据');
        return false;
      }
    } catch (e) {
      debugPrint('自动登录失败: $e');
      await clearCredentials();
      return false;
    }
  }

  /// 用户登录
  Future<User?> login({
    required String email,
    required String password,
    bool rememberCredentials = true,
  }) async {
    try {
      // 使用 PocketBase 认证
      final authData = await pb
          .collection('users')
          .authWithPassword(email, password);

      // 创建用户对象
      final user = User.fromJson(authData.record.toJson());

      // 尝试更新最后登录时间，如果失败则跳过
      try {
        // 先检查用户记录的完整性
        debugPrint('检查用户记录: ${authData.record.id}');
        debugPrint('用户名: ${authData.record.data['username']}');
        debugPrint('邮箱: ${authData.record.data['email']}');
        debugPrint('昵称: ${authData.record.data['nickname']}');

        // 简单更新，只更新lastLoginAt字段
        await pb
            .collection('users')
            .update(
              authData.record.id,
              body: {'lastLoginAt': DateTime.now().toIso8601String()},
            );
        debugPrint('最后登录时间更新成功');
      } catch (e) {
        debugPrint('更新最后登录时间失败，但不影响登录: $e');
        // 不抛出异常，登录仍然成功
      }

      // 更新当前用户
      _currentUserNotifier.value = user;

      // 保存登录凭据
      if (rememberCredentials) {
        await _saveCredentials(email: email, password: password);
      }

      debugPrint('用户登录成功: ${user.username}');
      return user;
    } catch (e) {
      debugPrint('登录失败: $e');
      throw Exception('登录失败: $e');
    }
  }

  /// 手机号登录或注册
  Future<User?> phoneLogin({
    required String phoneNumber,
    bool rememberCredentials = true,
  }) async {
    try {
      // 检查用户是否已存在
      final existingUsers = await pb
          .collection('users')
          .getList(page: 1, perPage: 1, filter: 'phone = "$phoneNumber"');

      if (existingUsers.items.isNotEmpty) {
        // 用户已存在，执行登录
        final userRecord = existingUsers.items.first;
        final user = User.fromJson(userRecord.toJson());

        // 暂时跳过最后登录时间更新，避免影响登录流程
        debugPrint('跳过最后登录时间更新，避免验证错误');

        _currentUserNotifier.value = user;

        if (rememberCredentials) {
          await _saveCredentials(phoneNumber: phoneNumber);
        }

        debugPrint('手机号登录成功: ${user.username}');
        return user;
      } else {
        // 用户不存在，执行注册
        return await _registerWithPhone(phoneNumber, rememberCredentials);
      }
    } catch (e) {
      debugPrint('手机号登录/注册失败: $e');
      throw Exception('手机号登录/注册失败: $e');
    }
  }

  /// 使用手机号注册
  Future<User?> _registerWithPhone(
    String phoneNumber,
    bool rememberCredentials,
  ) async {
    try {
      // 生成临时用户名和邮箱
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final tempUsername = 'user_$timestamp';
      final tempEmail = '$<EMAIL>';
      final tempPassword = 'temp_password_$timestamp';

      // 创建用户记录
      final userRecord = await pb
          .collection('users')
          .create(
            body: {
              'username': tempUsername,
              'email': tempEmail,
              'password': tempPassword,
              'passwordConfirm': tempPassword,
              'nickname': '钓友$timestamp',
              'phone': phoneNumber,
            },
          );

      // 使用创建的凭据登录
      await pb.collection('users').authWithPassword(tempEmail, tempPassword);

      final user = User.fromJson(userRecord.toJson());
      _currentUserNotifier.value = user;

      if (rememberCredentials) {
        await _saveCredentials(phoneNumber: phoneNumber);
      }

      debugPrint('手机号注册成功: ${user.username}');
      return user;
    } catch (e) {
      debugPrint('手机号注册失败: $e');
      throw Exception('手机号注册失败: $e');
    }
  }

  /// 用户注册
  Future<User?> register({
    required String email,
    required String password,
    required String username,
    required String nickname,
    String? bio,
  }) async {
    try {
      // 创建用户数据
      final userData = {
        'username': username,
        'email': email,
        'password': password,
        'passwordConfirm': password,
        'nickname': nickname,
        'bio': bio ?? '',
      };

      // 创建用户记录
      final userRecord = await pb.collection('users').create(body: userData);

      // 使用创建的凭据登录
      await pb.collection('users').authWithPassword(email, password);

      final user = User.fromJson(userRecord.toJson());
      _currentUserNotifier.value = user;

      debugPrint('用户注册成功: ${user.username}');
      return user;
    } catch (e) {
      debugPrint('注册失败: $e');

      String errorMessage = e.toString();
      if (errorMessage.contains('statusCode: 400')) {
        if (errorMessage.contains('username')) {
          throw Exception('用户名已被使用或格式不正确');
        } else if (errorMessage.contains('email')) {
          throw Exception('邮箱已被使用或格式不正确');
        } else {
          throw Exception('注册信息格式错误，请检查输入');
        }
      } else {
        throw Exception('注册失败: $e');
      }
    }
  }

  /// 退出登录
  Future<void> logout({bool clearSavedCredentials = false}) async {
    try {
      pb.authStore.clear();
      _currentUserNotifier.value = null;

      if (clearSavedCredentials) {
        await clearCredentials();
      }

      debugPrint('用户退出登录成功');
    } catch (e) {
      debugPrint('退出登录失败: $e');
      throw Exception('退出登录失败: $e');
    }
  }

  /// 保存登录凭据
  Future<void> _saveCredentials({
    String? email,
    String? password,
    String? phoneNumber,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (email != null && password != null) {
        await prefs.setString(_emailKey, email);
        await prefs.setString(_passwordKey, password);
        await prefs.remove(_phoneKey);
      } else if (phoneNumber != null) {
        await prefs.setString(_phoneKey, phoneNumber);
        await prefs.remove(_emailKey);
        await prefs.remove(_passwordKey);
      }

      await prefs.setBool(_autoLoginKey, true);
    } catch (e) {
      debugPrint('保存登录凭据失败: $e');
    }
  }

  /// 清除保存的凭据
  Future<void> clearCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_emailKey);
      await prefs.remove(_passwordKey);
      await prefs.remove(_phoneKey);
      await prefs.setBool(_autoLoginKey, false);
      debugPrint('已清除所有保存的登录凭据');
    } catch (e) {
      debugPrint('清除凭据失败: $e');
    }
  }

  /// 检查是否启用了自动登录
  Future<bool> isAutoLoginEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_autoLoginKey) ?? false;
    } catch (e) {
      debugPrint('检查自动登录状态失败: $e');
      return false;
    }
  }

  /// 设置自动登录开关
  Future<void> setAutoLoginEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_autoLoginKey, enabled);
      debugPrint('自动登录设置已更新: $enabled');

      if (!enabled) {
        // 如果禁用自动登录，清除保存的凭据
        await clearCredentials();
      }
    } catch (e) {
      debugPrint('设置自动登录失败: $e');
    }
  }

  /// 获取保存的登录信息（用于显示）
  Future<Map<String, String?>> getSavedLoginInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return {
        'email': prefs.getString(_emailKey),
        'phone': prefs.getString(_phoneKey),
        'autoLoginEnabled': (prefs.getBool(_autoLoginKey) ?? false).toString(),
      };
    } catch (e) {
      debugPrint('获取保存的登录信息失败: $e');
      return {};
    }
  }

  /// 检查是否需要登录
  bool requiresLogin(String action) {
    switch (action) {
      case 'post_spot':
      case 'add_comment':
      case 'like_spot':
      case 'profile_settings':
        return true;
      case 'view_spots':
      case 'search_spots':
      case 'view_map':
      default:
        return false;
    }
  }

  /// 更新当前用户信息
  ///
  /// [user] 更新后的用户信息
  void updateCurrentUser(User user) {
    _currentUserNotifier.value = user;
    debugPrint('✅ [认证服务] 当前用户信息已更新: ${user.username}');
  }

  /// 清理相关缓存
  ///
  /// 在用户登录/注销时调用，确保数据安全和隐私
  void _clearRelatedCaches() {
    try {
      // 清理钓点服务缓存（包括本地存储）
      Services.fishingSpot.clearAllCache().catchError((e) {
        debugPrint('⚠️ [认证服务] 清理钓点缓存失败: $e');
      });
      debugPrint('✅ [认证服务] 已清理钓点服务缓存');

      // 清理可见性服务缓存
      Services.spotVisibility.clearAllCache().catchError((e) {
        debugPrint('⚠️ [认证服务] 清理可见性缓存失败: $e');
      });
      debugPrint('✅ [认证服务] 已清理可见性服务缓存');

      // 清理社交服务缓存
      Services.social.clearAllCache().catchError((e) {
        debugPrint('⚠️ [认证服务] 清理社交服务缓存失败: $e');
      });
      debugPrint('✅ [认证服务] 已清理社交服务缓存');

      debugPrint('🔄 [认证服务] 用户状态变化，已清理所有相关缓存');
    } catch (e) {
      debugPrint('⚠️ [认证服务] 清理缓存时出错: $e');
    }
  }

  /// 释放资源
  void dispose() {
    _currentUserNotifier.dispose();
  }
}

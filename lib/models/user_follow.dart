/// 用户关注关系模型
/// 
/// 用于管理用户之间的关注关系
/// 遵循 PocketBase 关系型数据库设计原则
class UserFollow {
  /// 唯一标识符 (PocketBase 自动生成)
  final String id;

  /// 关注者用户ID
  final String followerId;

  /// 被关注者用户ID
  final String followingId;

  /// 创建时间 (PocketBase 自动管理)
  final DateTime created;

  /// 更新时间 (PocketBase 自动管理)
  final DateTime updated;

  UserFollow({
    required this.id,
    required this.followerId,
    required this.followingId,
    required this.created,
    required this.updated,
  });

  /// 从JSON创建关注关系对象
  factory UserFollow.fromJson(Map<String, dynamic> json) {
    return UserFollow(
      id: json['id'],
      followerId: json['follower_id'],
      followingId: json['following_id'],
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'follower_id': followerId,
      'following_id': followingId,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserFollow &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserFollow{id: $id, followerId: $followerId, followingId: $followingId}';
  }
}

import 'dart:convert';

/// 钓点可见性枚举
enum SpotVisibility {
  /// 私有 - 只有创建者可见
  private('PRIVATE', '私有', '只有您可以查看'),

  /// 好友可见 - 创建者的好友可见
  friendsOnly('FRIENDS_ONLY', '好友可见', '您的好友可以查看'),

  /// 条件可见 - 符合特定条件的用户可见
  conditional('CONDITIONAL', '条件可见', '符合条件的用户可以查看'),

  /// 完全公开 - 所有用户可见
  public('PUBLIC', '完全公开', '所有用户都可以查看');

  const SpotVisibility(this.value, this.displayName, this.description);

  /// 数据库存储值
  final String value;

  /// 显示名称
  final String displayName;

  /// 描述文本
  final String description;

  /// 从字符串值创建枚举
  static SpotVisibility fromString(String value) {
    return SpotVisibility.values.firstWhere(
      (v) => v.value == value,
      orElse: () => SpotVisibility.public,
    );
  }

  /// 获取可见性图标
  String get iconName {
    switch (this) {
      case SpotVisibility.private:
        return 'lock';
      case SpotVisibility.friendsOnly:
        return 'people';
      case SpotVisibility.conditional:
        return 'star';
      case SpotVisibility.public:
        return 'public';
    }
  }

  /// 获取可见性颜色
  String get colorName {
    switch (this) {
      case SpotVisibility.private:
        return 'red';
      case SpotVisibility.friendsOnly:
        return 'orange';
      case SpotVisibility.conditional:
        return 'blue';
      case SpotVisibility.public:
        return 'green';
    }
  }
}

/// 可见性条件类型
enum VisibilityConditionType {
  /// 积分赠送条件
  pointsDonated('POINTS_DONATED', '积分赠送', '向作者赠送指定积分后可查看'),

  /// 等级要求条件
  levelRequired('LEVEL_REQUIRED', '等级要求', '达到指定等级后可查看'),

  /// 付费查看条件
  payToView('PAY_TO_VIEW', '付费查看', '支付指定积分后可查看'),

  /// 自定义条件
  custom('CUSTOM', '自定义条件', '满足自定义条件后可查看');

  const VisibilityConditionType(this.value, this.displayName, this.description);

  final String value;
  final String displayName;
  final String description;

  static VisibilityConditionType fromString(String value) {
    return VisibilityConditionType.values.firstWhere(
      (t) => t.value == value,
      orElse: () => VisibilityConditionType.pointsDonated,
    );
  }
}

/// 钓点可见性条件模型
class SpotVisibilityCondition {
  /// 条件ID
  final String id;

  /// 关联的钓点ID
  final String spotId;

  /// 条件类型
  final VisibilityConditionType type;

  /// 条件值（JSON格式）
  final Map<String, dynamic> value;

  /// 创建时间
  final DateTime created;

  /// 更新时间
  final DateTime updated;

  SpotVisibilityCondition({
    required this.id,
    required this.spotId,
    required this.type,
    required this.value,
    required this.created,
    required this.updated,
  });

  /// 从JSON创建条件对象
  factory SpotVisibilityCondition.fromJson(Map<String, dynamic> json) {
    return SpotVisibilityCondition(
      id: json['id'] ?? '',
      spotId: json['spot_id'] ?? '',
      type: VisibilityConditionType.fromString(json['condition_type'] ?? ''),
      value:
          json['condition_value'] is String
              ? jsonDecode(json['condition_value'])
              : json['condition_value'] ?? {},
      created:
          json['created'] != null
              ? DateTime.parse(json['created'])
              : DateTime.now(),
      updated:
          json['updated'] != null
              ? DateTime.parse(json['updated'])
              : DateTime.now(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'spot_id': spotId,
      'condition_type': type.value,
      'condition_value': jsonEncode(value),
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  /// 获取条件描述文本
  String getConditionDescription() {
    switch (type) {
      case VisibilityConditionType.pointsDonated:
        final minPoints = value['minPoints'] as int? ?? 0;
        return '向作者赠送 $minPoints 积分后可查看';

      case VisibilityConditionType.levelRequired:
        final minLevel = value['minLevel'] as int? ?? 1;
        return '达到 $minLevel 级后可查看';

      case VisibilityConditionType.payToView:
        final price = value['price'] as int? ?? 0;
        return '支付 $price 积分后可查看';

      case VisibilityConditionType.custom:
        return value['description'] as String? ?? '满足自定义条件后可查看';
    }
  }

  /// 检查用户是否满足条件
  Future<bool> checkCondition(String userId) async {
    switch (type) {
      case VisibilityConditionType.pointsDonated:
        final minPoints = value['minPoints'] as int? ?? 0;
        return await _checkPointsDonated(userId, minPoints);

      case VisibilityConditionType.levelRequired:
        final minLevel = value['minLevel'] as int? ?? 1;
        return await _checkUserLevel(userId, minLevel);

      case VisibilityConditionType.payToView:
        final price = value['price'] as int? ?? 0;
        return await _checkPayToView(userId, price);

      case VisibilityConditionType.custom:
        // 自定义条件需要具体实现
        return false;
    }
  }

  /// 检查积分赠送条件
  Future<bool> _checkPointsDonated(String userId, int minPoints) async {
    // 这里需要调用相应的服务来检查
    // 暂时返回false，具体实现在SpotVisibilityService中
    return false;
  }

  /// 检查用户等级条件
  Future<bool> _checkUserLevel(String userId, int minLevel) async {
    // 这里需要调用相应的服务来检查
    // 暂时返回false，具体实现在SpotVisibilityService中
    return false;
  }

  /// 检查付费查看条件
  Future<bool> _checkPayToView(String userId, int price) async {
    // 这里需要调用相应的服务来检查
    // 暂时返回false，具体实现在SpotVisibilityService中
    return false;
  }
}

/// 用户积分赠送记录
class UserPointDonation {
  /// 记录ID
  final String id;

  /// 赠送者ID
  final String donorId;

  /// 接收者ID
  final String recipientId;

  /// 赠送积分数
  final int points;

  /// 赠送原因
  final String? reason;

  /// 关联的钓点ID（可选）
  final String? spotId;

  /// 创建时间
  final DateTime created;

  UserPointDonation({
    required this.id,
    required this.donorId,
    required this.recipientId,
    required this.points,
    this.reason,
    this.spotId,
    required this.created,
  });

  /// 从JSON创建赠送记录对象
  factory UserPointDonation.fromJson(Map<String, dynamic> json) {
    return UserPointDonation(
      id: json['id'] ?? '',
      donorId: json['donor_id'] ?? '',
      recipientId: json['recipient_id'] ?? '',
      points: json['points'] as int? ?? 0,
      reason: json['reason'],
      spotId: json['spot_id'],
      created:
          json['created'] != null
              ? DateTime.parse(json['created'])
              : DateTime.now(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'donor_id': donorId,
      'recipient_id': recipientId,
      'points': points,
      'reason': reason,
      'spot_id': spotId,
      'created': created.toIso8601String(),
    };
  }
}

/// 钓点访问权限缓存
class SpotAccessCache {
  /// 缓存ID
  final String id;

  /// 用户ID
  final String userId;

  /// 钓点ID
  final String spotId;

  /// 是否有访问权限
  final bool hasAccess;

  /// 访问权限原因
  final String? accessReason;

  /// 缓存过期时间
  final DateTime? expiresAt;

  /// 创建时间
  final DateTime created;

  /// 更新时间
  final DateTime updated;

  SpotAccessCache({
    required this.id,
    required this.userId,
    required this.spotId,
    required this.hasAccess,
    this.accessReason,
    this.expiresAt,
    required this.created,
    required this.updated,
  });

  /// 从JSON创建缓存对象
  factory SpotAccessCache.fromJson(Map<String, dynamic> json) {
    return SpotAccessCache(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      spotId: json['spot_id'] ?? '',
      hasAccess: json['has_access'] as bool? ?? false,
      accessReason: json['access_reason'],
      expiresAt:
          json['expires_at'] != null
              ? DateTime.parse(json['expires_at'])
              : null,
      created:
          json['created'] != null
              ? DateTime.parse(json['created'])
              : DateTime.now(),
      updated:
          json['updated'] != null
              ? DateTime.parse(json['updated'])
              : DateTime.now(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'spot_id': spotId,
      'has_access': hasAccess,
      'access_reason': accessReason,
      'expires_at': expiresAt?.toIso8601String(),
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  /// 检查缓存是否过期
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }
}

/// 消息模型
/// 
/// 用于私信功能的消息数据结构
class Message {
  /// 消息ID
  final String id;
  
  /// 发送者ID
  final String senderId;
  
  /// 接收者ID
  final String receiverId;
  
  /// 消息内容
  final String content;
  
  /// 发送时间
  final DateTime sentAt;
  
  /// 是否已读
  final bool isRead;

  Message({
    String? id,
    required this.senderId,
    required this.receiverId,
    required this.content,
    DateTime? sentAt,
    this.isRead = false,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString(),
       sentAt = sentAt ?? DateTime.now();

  /// 从JSON创建消息对象
  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'],
      senderId: json['sender_id'],
      receiverId: json['receiver_id'],
      content: json['content'],
      sentAt: DateTime.parse(json['sent_at']),
      isRead: json['is_read'] ?? false,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sender_id': senderId,
      'receiver_id': receiverId,
      'content': content,
      'sent_at': sentAt.toIso8601String(),
      'is_read': isRead,
    };
  }

  /// 创建副本
  Message copyWith({
    String? id,
    String? senderId,
    String? receiverId,
    String? content,
    DateTime? sentAt,
    bool? isRead,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      content: content ?? this.content,
      sentAt: sentAt ?? this.sentAt,
      isRead: isRead ?? this.isRead,
    );
  }

  @override
  String toString() {
    return 'Message(id: $id, senderId: $senderId, receiverId: $receiverId, content: $content, sentAt: $sentAt, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Message && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

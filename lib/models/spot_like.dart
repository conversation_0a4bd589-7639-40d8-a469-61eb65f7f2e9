/// 钓点点赞模型
/// 
/// 用于管理用户对钓点的点赞/不喜欢关系
/// 遵循 PocketBase 关系型数据库设计原则
class SpotLike {
  /// 唯一标识符 (PocketBase 自动生成)
  final String id;

  /// 用户ID
  final String userId;

  /// 钓点ID
  final String spotId;

  /// 是否点赞 (true: 点赞, false: 取消点赞)
  final bool isLike;

  /// 创建时间 (PocketBase 自动管理)
  final DateTime created;

  /// 更新时间 (PocketBase 自动管理)
  final DateTime updated;

  SpotLike({
    required this.id,
    required this.userId,
    required this.spotId,
    required this.isLike,
    required this.created,
    required this.updated,
  });

  /// 从JSON创建点赞对象
  factory SpotLike.fromJson(Map<String, dynamic> json) {
    return SpotLike(
      id: json['id'],
      userId: json['user_id'],
      spotId: json['spot_id'],
      isLike: json['is_like'] ?? false,
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'spot_id': spotId,
      'is_like': isLike,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SpotLike &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SpotLike{id: $id, userId: $userId, spotId: $spotId, isLike: $isLike}';
  }
}

/// 图片上传结果模型
class ImageUploadResult {
  /// 原图URL
  final String originalUrl;
  
  /// 缩略图URL
  final String thumbnailUrl;
  
  /// 文件名
  final String fileName;
  
  /// 文件大小（字节）
  final int fileSize;
  
  /// 图片宽度
  final int width;
  
  /// 图片高度
  final int height;

  ImageUploadResult({
    required this.originalUrl,
    required this.thumbnailUrl,
    required this.fileName,
    required this.fileSize,
    required this.width,
    required this.height,
  });
}

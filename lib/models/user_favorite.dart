/// 用户收藏钓点关系模型
/// 
/// 用于管理用户对钓点的收藏关系
/// 遵循 PocketBase 关系型数据库设计原则
class UserFavorite {
  /// 唯一标识符 (PocketBase 自动生成)
  final String id;

  /// 用户ID
  final String userId;

  /// 钓点ID
  final String spotId;

  /// 创建时间 (PocketBase 自动管理)
  final DateTime created;

  /// 更新时间 (PocketBase 自动管理)
  final DateTime updated;

  UserFavorite({
    required this.id,
    required this.userId,
    required this.spotId,
    required this.created,
    required this.updated,
  });

  /// 从JSON创建收藏关系对象
  factory UserFavorite.fromJson(Map<String, dynamic> json) {
    return UserFavorite(
      id: json['id'],
      userId: json['user_id'],
      spotId: json['spot_id'],
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'spot_id': spotId,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserFavorite &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserFavorite{id: $id, userId: $userId, spotId: $spotId}';
  }
}

/// 对话模型
/// 
/// 用于消息列表显示的对话信息
class Conversation {
  /// 对话ID
  final String id;
  
  /// 对话对象用户ID
  final String otherUserId;
  
  /// 对话对象用户名
  final String otherUserName;
  
  /// 对话对象头像URL
  final String? otherUserAvatar;
  
  /// 最后一条消息内容
  final String? lastMessage;
  
  /// 最后一条消息时间
  final DateTime? lastMessageTime;
  
  /// 最后一条消息类型
  final String? lastMessageType; // text, image, system
  
  /// 未读消息数
  final int unreadCount;
  
  /// 是否为陌生人对话（需要验证）
  final bool isPending;
  
  /// 是否被置顶
  final bool isPinned;

  Conversation({
    required this.id,
    required this.otherUserId,
    required this.otherUserName,
    this.otherUserAvatar,
    this.lastMessage,
    this.lastMessageTime,
    this.lastMessageType = 'text',
    this.unreadCount = 0,
    this.isPending = false,
    this.isPinned = false,
  });

  /// 从JSON创建对话对象
  factory Conversation.fromJson(Map<String, dynamic> json) {
    return Conversation(
      id: json['id'],
      otherUserId: json['other_user_id'],
      otherUserName: json['other_user_name'],
      otherUserAvatar: json['other_user_avatar'],
      lastMessage: json['last_message'],
      lastMessageTime: json['last_message_time'] != null 
          ? DateTime.parse(json['last_message_time'])
          : null,
      lastMessageType: json['last_message_type'] ?? 'text',
      unreadCount: json['unread_count'] ?? 0,
      isPending: json['is_pending'] ?? false,
      isPinned: json['is_pinned'] ?? false,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'other_user_id': otherUserId,
      'other_user_name': otherUserName,
      'other_user_avatar': otherUserAvatar,
      'last_message': lastMessage,
      'last_message_time': lastMessageTime?.toIso8601String(),
      'last_message_type': lastMessageType,
      'unread_count': unreadCount,
      'is_pending': isPending,
      'is_pinned': isPinned,
    };
  }

  /// 获取显示的最后消息文本
  String get displayLastMessage {
    if (lastMessage == null || lastMessage!.isEmpty) {
      return '暂无消息';
    }
    
    switch (lastMessageType) {
      case 'image':
        return '[图片]';
      case 'system':
        return '[系统消息]';
      default:
        return lastMessage!;
    }
  }

  /// 获取显示的时间文本
  String get displayTime {
    if (lastMessageTime == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(lastMessageTime!);
    
    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${lastMessageTime!.hour.toString().padLeft(2, '0')}:${lastMessageTime!.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${lastMessageTime!.month}月${lastMessageTime!.day}日';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Conversation &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
/// 联系人分类模型
/// 
/// 用于联系人页面的分类显示
class ContactCategory {
  /// 分类名称
  final String name;
  
  /// 分类图标
  final String icon;
  
  /// 联系人数量
  final int count;
  
  /// 分类类型
  final ContactCategoryType type;
  
  /// 是否显示未读角标
  final bool showBadge;
  
  /// 未读数量
  final int badgeCount;

  ContactCategory({
    required this.name,
    required this.icon,
    required this.count,
    required this.type,
    this.showBadge = false,
    this.badgeCount = 0,
  });

  /// 获取显示的数量文本
  String get displayCount {
    if (count == 0) return '';
    if (count > 999) return '999+';
    return '($count)';
  }
}

/// 联系人分类类型
enum ContactCategoryType {
  friends,      // 好友（互相关注）
  following,    // 我关注的
  followers,    // 关注我的
  blocked,      // 黑名单
}

/// 联系人分类扩展
extension ContactCategoryTypeExtension on ContactCategoryType {
  String get displayName {
    switch (this) {
      case ContactCategoryType.friends:
        return '好友';
      case ContactCategoryType.following:
        return '我关注的';
      case ContactCategoryType.followers:
        return '关注我的';
      case ContactCategoryType.blocked:
        return '黑名单';
    }
  }
  
  String get iconName {
    switch (this) {
      case ContactCategoryType.friends:
        return '👥';
      case ContactCategoryType.following:
        return '👁️';
      case ContactCategoryType.followers:
        return '👤';
      case ContactCategoryType.blocked:
        return '🚫';
    }
  }
}
/// Emoji标记模型
class EmojiMarker {
  /// 标记类型
  final String type;
  
  /// 显示的emoji
  final String emoji;
  
  /// 标记名称
  final String name;
  
  /// 描述
  final String description;

  const EmojiMarker({
    required this.type,
    required this.emoji,
    required this.name,
    required this.description,
  });
}

/// 预定义的钓点标记类型
class FishingSpotMarkers {
  static const List<EmojiMarker> spotTypes = [
    EmojiMarker(
      type: 'freshwater',
      emoji: '🐟️',
      name: '淡水钓点',
      description: '淡水钓鱼点',
    ),
    EmojiMarker(
      type: 'saltwater',
      emoji: '🌊',
      name: '海水钓点',
      description: '海水钓鱼点',
    ),
    EmojiMarker(
      type: 'pond',
      emoji: '🏞️',
      name: '池塘',
      description: '人工池塘',
    ),
    EmojiMarker(
      type: 'river',
      emoji: '🏔️',
      name: '河流',
      description: '天然河流',
    ),
    EmojiMarker(
      type: 'lake',
      emoji: '🏔️',
      name: '湖泊',
      description: '天然湖泊',
    ),
    EmojiMarker(
      type: 'reservoir',
      emoji: '🏗️',
      name: '水库',
      description: '人工水库',
    ),
  ];

  static const List<EmojiMarker> fishTypes = [
    EmojiMarker(
      type: 'carp',
      emoji: '🐟',
      name: '鲤鱼',
      description: '鲤鱼类',
    ),
    EmojiMarker(
      type: 'crucian',
      emoji: '🐠',
      name: '鲫鱼',
      description: '鲫鱼类',
    ),
    EmojiMarker(
      type: 'bass',
      emoji: '🐡',
      name: '鲈鱼',
      description: '鲈鱼类',
    ),
    EmojiMarker(
      type: 'catfish',
      emoji: '🦈',
      name: '鲶鱼',
      description: '鲶鱼类',
    ),
    EmojiMarker(
      type: 'grass_carp',
      emoji: '🐠',
      name: '草鱼',
      description: '草鱼类',
    ),
    EmojiMarker(
      type: 'silver_carp',
      emoji: '🐡',
      name: '鲢鳙',
      description: '鲢鳙类',
    ),
  ];

  /// 根据钓点类型获取对应的emoji
  static String getSpotTypeEmoji(String? spotType) {
    if (spotType == null) return '🐟️';

    final marker = spotTypes.firstWhere(
      (m) => m.type == spotType || m.name == spotType,
      orElse: () => spotTypes.first,
    );
    return marker.emoji;
  }

  /// 根据鱼类类型获取对应的emoji
  static String getFishTypeEmoji(String? fishType) {
    if (fishType == null) return '🐠'; // 改为与钓点类型不同的默认emoji

    final marker = fishTypes.firstWhere(
      (m) => m.type == fishType || m.name == fishType,
      orElse: () => fishTypes.first,
    );
    return marker.emoji;
  }
}
/// 钓点评论模型
///
/// 用于管理钓点相关的评论
/// 遵循 PocketBase 关系型数据库设计原则
class SpotComment {
  /// 唯一标识符 (PocketBase 自动生成)
  final String id;

  /// 钓点ID
  final String spotId;

  /// 评论者用户ID
  final String userId;

  /// 评论内容
  final String content;

  /// 评分 (1-5星，可选)
  int? rating;

  /// 创建时间 (PocketBase 自动管理)
  final DateTime created;

  /// 更新时间 (PocketBase 自动管理)
  final DateTime updated;

  SpotComment({
    required this.id,
    required this.spotId,
    required this.userId,
    required this.content,
    this.rating,
    required this.created,
    required this.updated,
  });

  /// 从JSON创建评论对象
  factory SpotComment.fromJson(Map<String, dynamic> json) {
    return SpotComment(
      id: json['id'],
      spotId: json['spot_id'],
      userId: json['user_id'],
      content: json['content'],
      rating: json['rating'],
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'spot_id': spotId,
      'user_id': userId,
      'content': content,
      'rating': rating,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SpotComment &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SpotComment{id: $id, spotId: $spotId, userId: $userId, content: $content}';
  }

  /// 兼容性方法：获取用户名（兼容旧代码）
  String get username => userId; // 临时返回用户ID，实际应该通过关联查询获取用户名

  /// 兼容性方法：获取创建时间（兼容旧代码）
  DateTime get createdAt => created;
}

/// 兼容性：旧的 Comment 类别名
typedef Comment = SpotComment;

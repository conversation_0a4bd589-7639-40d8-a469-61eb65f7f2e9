import 'package:flutter/material.dart';
import '../models/emoji_marker.dart';

/// Emoji标记选择器
class EmojiMarkerPicker extends StatefulWidget {
  final String? selectedSpotType;
  final String? selectedFishType;
  final Function(String spotType, String fishType) onSelectionChanged;

  const EmojiMarkerPicker({
    super.key,
    this.selectedSpotType,
    this.selectedFishType,
    required this.onSelectionChanged,
  });

  @override
  State<EmojiMarkerPicker> createState() => _EmojiMarkerPickerState();
}

class _EmojiMarkerPickerState extends State<EmojiMarkerPicker> {
  String? _selectedSpotType;
  String? _selectedFishType;

  @override
  void initState() {
    super.initState();
    _selectedSpotType = widget.selectedSpotType ?? FishingSpotMarkers.spotTypes.first.type;
    _selectedFishType = widget.selectedFishType ?? FishingSpotMarkers.fishTypes.first.type;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '钓点类型',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16), // 增加空行
        _buildSpotTypeGrid(),
        const SizedBox(height: 16),
        const Text(
          '鱼类类型',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        _buildFishTypeGrid(),
      ],
    );
  }

  Widget _buildSpotTypeGrid() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: FishingSpotMarkers.spotTypes.map((marker) {
        final isSelected = _selectedSpotType == marker.type;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedSpotType = marker.type;
            });
            widget.onSelectionChanged(_selectedSpotType!, _selectedFishType!);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected
                  ? Colors.blue.withValues(alpha: 0.15)
                  : Colors.grey.withValues(alpha: 0.08),
              borderRadius: BorderRadius.circular(20), // 椭圆形
              border: isSelected
                  ? Border.all(color: Colors.blue, width: 1.5)
                  : Border.all(color: Colors.grey.shade300, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  marker.emoji,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 6),
                Text(
                  marker.name,
                  style: TextStyle(
                    fontSize: 13,
                    color: isSelected ? Colors.blue.shade700 : Colors.black87,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildFishTypeGrid() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: FishingSpotMarkers.fishTypes.map((marker) {
        final isSelected = _selectedFishType == marker.type;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedFishType = marker.type;
            });
            widget.onSelectionChanged(_selectedSpotType!, _selectedFishType!);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected
                  ? Colors.green.withValues(alpha: 0.15)
                  : Colors.grey.withValues(alpha: 0.08),
              borderRadius: BorderRadius.circular(20), // 椭圆形
              border: isSelected
                  ? Border.all(color: Colors.green, width: 1.5)
                  : Border.all(color: Colors.grey.shade300, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  marker.emoji,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 6),
                Text(
                  marker.name,
                  style: TextStyle(
                    fontSize: 13,
                    color: isSelected ? Colors.green.shade700 : Colors.black87,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}
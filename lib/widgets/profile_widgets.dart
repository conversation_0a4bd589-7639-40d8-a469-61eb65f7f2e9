import 'package:flutter/material.dart';

/// 个人资料页面的UI组件集合
class ProfileWidgets {
  /// 用户信息卡片组件
  static Widget buildUserInfoCard({
    required String? avatarUrl,
    required String nickname,
    required String? bio,
    required int points,
    required VoidCallback onEditProfile,
    required VoidCallback onAvatarTap,
    bool isLoggedIn = true,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 获取屏幕高度，设置为36%
        final screenHeight = MediaQuery.of(context).size.height;
        final cardHeight = screenHeight * 0.36;

        return Container(
          width: double.infinity,
          height: cardHeight,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF4A90E2), // 海洋蓝
                Color(0xFF50C878), // 青绿色
              ],
            ),
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(25),
              bottomRight: Radius.circular(25),
            ),
          ),
          child:
              isLoggedIn
                  ? _buildLoggedInContent(
                    avatarUrl: avatarUrl,
                    nickname: nickname,
                    bio: bio,
                    onEditProfile: onEditProfile,
                    onAvatarTap: onAvatarTap,
                  )
                  : _buildNotLoggedInContent(onEditProfile),
        );
      },
    );
  }

  /// 统计数据卡片组件
  static Widget buildStatsCard({
    required String count,
    required String label,
    required VoidCallback onTap,
    bool hasNotification = false,
    Color? backgroundColor,
    Color? textColor,
  }) {
    // 为每个卡片定义渐变色背景
    final gradientColors =
        backgroundColor != null
            ? [backgroundColor, backgroundColor.withOpacity(0.7)]
            : [
              const Color(0xFF4A90E2).withOpacity(0.8),
              const Color(0xFF50C878).withOpacity(0.8),
            ];

    final textStyle = TextStyle(
      fontSize: 18,
      fontWeight: FontWeight.bold,
      color: textColor ?? Colors.white,
    );

    final labelStyle = TextStyle(
      fontSize: 12,
      color:
          textColor != null
              ? textColor.withOpacity(0.8)
              : Colors.white.withOpacity(0.9),
      fontWeight: FontWeight.w500,
    );

    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 80,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: gradientColors,
            ),
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(count, style: textStyle, textAlign: TextAlign.center),
                    const SizedBox(height: 4),
                    Text(label, style: labelStyle, textAlign: TextAlign.center),
                  ],
                ),
              ),
              // 新消息提示红点
              if (hasNotification)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 功能菜单项组件
  static Widget buildMenuTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? iconColor,
    bool showArrow = true,
    Widget? trailing,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            blurRadius: 6,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ListTile(
        onTap: onTap,
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: (iconColor ?? const Color(0xFF4A90E2)).withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            color: iconColor ?? const Color(0xFF4A90E2),
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Color(0xFF333333),
          ),
        ),
        trailing:
            trailing ??
            (showArrow
                ? Icon(Icons.chevron_right, color: Colors.grey.shade400)
                : null),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      ),
    );
  }

  /// 分组标题组件
  static Widget buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Colors.grey.shade600,
        ),
      ),
    );
  }

  /// 构建已登录用户的内容
  static Widget _buildLoggedInContent({
    required String? avatarUrl,
    required String nickname,
    required String? bio,
    required VoidCallback onEditProfile,
    required VoidCallback onAvatarTap,
  }) {
    return Stack(
      children: [
        // 头像区域（居中）
        Center(
          child: GestureDetector(
            onTap: onAvatarTap,
            child: Stack(
              children: [
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 3),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: 47,
                    backgroundColor: Colors.white.withOpacity(0.3),
                    backgroundImage:
                        avatarUrl != null && avatarUrl.isNotEmpty
                            ? NetworkImage(avatarUrl)
                            : null,
                    child:
                        avatarUrl == null || avatarUrl.isEmpty
                            ? const Icon(
                              Icons.person_add,
                              size: 40,
                              color: Colors.white,
                            )
                            : null,
                  ),
                ),
                // 编辑图标
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      size: 18,
                      color: Color(0xFF4A90E2),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // 用户名和编辑按钮（左下角）
        Positioned(
          bottom: 0,
          left: 20,
          child: Row(
            children: [
              Text(
                nickname,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 8),
              GestureDetector(
                onTap: onEditProfile,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.edit_note,
                    size: 18,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建未登录用户的内容
  static Widget _buildNotLoggedInContent(VoidCallback onLogin) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            '未登录',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '登录后查看更多功能',
            style: TextStyle(fontSize: 16, color: Colors.white),
          ),
        ],
      ),
    );
  }
}

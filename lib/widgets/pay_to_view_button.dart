import 'package:flutter/material.dart';
import '../models/fishing_spot.dart';
import '../models/user.dart';
import '../models/spot_visibility.dart';
import '../services/service_locator.dart';

/// 付费查看钓点按钮组件
///
/// 用于在钓点详情页面显示付费查看选项
class PayToViewButton extends StatefulWidget {
  /// 钓点信息
  final FishingSpot spot;

  /// 当前用户
  final User? currentUser;

  /// 支付成功回调
  final VoidCallback? onPaymentSuccess;

  /// 按钮样式
  final ButtonStyle? style;

  /// 是否显示为紧凑模式
  final bool compact;

  const PayToViewButton({
    super.key,
    required this.spot,
    this.currentUser,
    this.onPaymentSuccess,
    this.style,
    this.compact = false,
  });

  @override
  State<PayToViewButton> createState() => _PayToViewButtonState();
}

class _PayToViewButtonState extends State<PayToViewButton> {
  bool _isLoading = false;
  bool _hasPaid = false;

  @override
  void initState() {
    super.initState();
    _checkPaymentStatus();
  }

  /// 检查用户是否已经支付过
  Future<void> _checkPaymentStatus() async {
    if (widget.currentUser == null ||
        widget.spot.visibility != SpotVisibility.conditional ||
        widget.spot.visibilityConditions?['type'] != 'PAY_TO_VIEW') {
      return;
    }

    final hasPaid = await Services.spotVisibility.canUserViewSpot(
      widget.currentUser!.id,
      widget.spot,
    );

    if (mounted) {
      setState(() {
        _hasPaid = hasPaid;
      });
    }
  }

  /// 处理付费查看
  Future<void> _handlePayToView() async {
    if (widget.currentUser == null) {
      _showMessage('请先登录', isError: true);
      return;
    }

    if (widget.spot.visibility != SpotVisibility.conditional ||
        widget.spot.visibilityConditions?['type'] != 'PAY_TO_VIEW') {
      _showMessage('此钓点不支持付费查看', isError: true);
      return;
    }

    final price = widget.spot.visibilityConditions!['price'] as int? ?? 0;

    // 显示确认对话框
    final confirmed = await _showPaymentConfirmDialog(price);
    if (!confirmed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await Services.spotVisibility.payToViewSpot(
        widget.spot.id,
        widget.spot.userId,
        price,
      );

      if (success) {
        setState(() {
          _hasPaid = true;
        });
        _showMessage('支付成功！现在可以查看钓点详情了', isError: false);
        widget.onPaymentSuccess?.call();
      } else {
        _showMessage('支付失败，请重试', isError: true);
      }
    } catch (e) {
      _showMessage('支付失败: $e', isError: true);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 显示支付确认对话框
  Future<bool> _showPaymentConfirmDialog(int price) async {
    final currentUser = widget.currentUser!;

    return await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('付费查看钓点'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('钓点名称: ${widget.spot.name}'),
                    const SizedBox(height: 8),
                    Text('查看费用: $price 积分'),
                    const SizedBox(height: 8),
                    Text('当前积分: ${currentUser.points}'),
                    const SizedBox(height: 16),
                    if (currentUser.points < price)
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.red.withValues(alpha: 0.3),
                          ),
                        ),
                        child: const Row(
                          children: [
                            Icon(Icons.warning, color: Colors.red, size: 20),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                '积分不足，无法支付',
                                style: TextStyle(color: Colors.red),
                              ),
                            ),
                          ],
                        ),
                      )
                    else
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.green.withValues(alpha: 0.3),
                          ),
                        ),
                        child: const Row(
                          children: [
                            Icon(Icons.info, color: Colors.green, size: 20),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                '支付后即可查看钓点的详细信息和位置',
                                style: TextStyle(color: Colors.green),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('取消'),
                  ),
                  ElevatedButton(
                    onPressed:
                        currentUser.points >= price
                            ? () => Navigator.of(context).pop(true)
                            : null,
                    child: const Text('确认支付'),
                  ),
                ],
              ),
        ) ??
        false;
  }

  /// 显示消息
  void _showMessage(String message, {required bool isError}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: Duration(seconds: isError ? 4 : 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 如果不是付费查看钓点，不显示按钮
    if (widget.spot.visibility != SpotVisibility.conditional ||
        widget.spot.visibilityConditions?['type'] != 'PAY_TO_VIEW') {
      return const SizedBox.shrink();
    }

    // 如果是钓点创建者，不显示按钮
    if (widget.currentUser?.id == widget.spot.userId) {
      return const SizedBox.shrink();
    }

    final price = widget.spot.visibilityConditions!['price'] as int? ?? 0;

    if (_hasPaid) {
      // 已支付状态
      return widget.compact
          ? Chip(
            avatar: const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 16,
            ),
            label: const Text('已解锁'),
            backgroundColor: Colors.green.withValues(alpha: 0.1),
          )
          : Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.check_circle, color: Colors.green, size: 20),
                SizedBox(width: 8),
                Text(
                  '已解锁此钓点',
                  style: TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          );
    }

    // 未支付状态
    if (widget.compact) {
      return OutlinedButton.icon(
        onPressed: _isLoading ? null : _handlePayToView,
        icon:
            _isLoading
                ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
                : const Icon(Icons.payment, size: 16),
        label: Text('$price积分解锁'),
        style: widget.style,
      );
    }

    return ElevatedButton.icon(
      onPressed: _isLoading ? null : _handlePayToView,
      icon:
          _isLoading
              ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
              : const Icon(Icons.payment),
      label: Text('支付 $price 积分查看'),
      style:
          widget.style ??
          ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
    );
  }
}

import 'package:flutter/material.dart';
import '../models/spot_visibility.dart';

/// 钓点可见性选择器组件
///
/// 用于在创建或编辑钓点时设置可见性级别和条件
class SpotVisibilitySelector extends StatefulWidget {
  /// 初始可见性设置
  final SpotVisibility initialVisibility;

  /// 初始可见性条件
  final Map<String, dynamic>? initialConditions;

  /// 可见性变更回调
  final Function(SpotVisibility visibility, Map<String, dynamic>? conditions)
  onChanged;

  /// 是否启用编辑
  final bool enabled;

  const SpotVisibilitySelector({
    Key? key,
    this.initialVisibility = SpotVisibility.public,
    this.initialConditions,
    required this.onChanged,
    this.enabled = true,
  }) : super(key: key);

  @override
  State<SpotVisibilitySelector> createState() => _SpotVisibilitySelectorState();
}

class _SpotVisibilitySelectorState extends State<SpotVisibilitySelector> {
  late SpotVisibility _selectedVisibility;
  Map<String, dynamic> _conditions = {};

  @override
  void initState() {
    super.initState();
    _selectedVisibility = widget.initialVisibility;
    _conditions = Map.from(widget.initialConditions ?? {});
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                Icon(Icons.visibility, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  '钓点可见性',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 可见性选项
            ...SpotVisibility.values.map(
              (visibility) => _buildVisibilityOption(visibility),
            ),

            // 条件设置
            if (_selectedVisibility == SpotVisibility.conditional) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              _buildConditionSettings(),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建可见性选项
  Widget _buildVisibilityOption(SpotVisibility visibility) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: RadioListTile<SpotVisibility>(
        title: Row(
          children: [
            _getVisibilityIcon(visibility),
            const SizedBox(width: 8),
            Text(
              visibility.displayName,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ],
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(left: 32),
          child: Text(
            visibility.description,
            style: TextStyle(color: Colors.grey[600], fontSize: 13),
          ),
        ),
        value: visibility,
        groupValue: _selectedVisibility,
        onChanged:
            widget.enabled
                ? (value) {
                  setState(() {
                    _selectedVisibility = value!;
                    if (value != SpotVisibility.conditional) {
                      _conditions.clear();
                    }
                  });
                  _notifyChange();
                }
                : null,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8),
      ),
    );
  }

  /// 获取可见性图标
  Widget _getVisibilityIcon(SpotVisibility visibility) {
    IconData iconData;
    Color color;

    switch (visibility) {
      case SpotVisibility.private:
        iconData = Icons.lock;
        color = Colors.red;
        break;
      case SpotVisibility.friendsOnly:
        iconData = Icons.people;
        color = Colors.orange;
        break;
      case SpotVisibility.conditional:
        iconData = Icons.star;
        color = Colors.blue;
        break;
      case SpotVisibility.public:
        iconData = Icons.public;
        color = Colors.green;
        break;
    }

    return Icon(iconData, color: color, size: 20);
  }

  /// 构建条件设置区域
  Widget _buildConditionSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.settings, color: Colors.blue[700], size: 20),
              const SizedBox(width: 8),
              Text(
                '可见条件设置',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 条件类型选择
          DropdownButtonFormField<String>(
            value: _conditions['type'],
            decoration: const InputDecoration(
              labelText: '条件类型',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: const [
              DropdownMenuItem(value: 'POINTS_DONATED', child: Text('积分赠送')),
              DropdownMenuItem(value: 'LEVEL_REQUIRED', child: Text('等级要求')),
              DropdownMenuItem(value: 'PAY_TO_VIEW', child: Text('付费查看')),
            ],
            onChanged:
                widget.enabled
                    ? (value) {
                      setState(() {
                        _conditions['type'] = value;
                        // 清除其他条件值
                        _conditions.removeWhere((key, _) => key != 'type');
                      });
                      _notifyChange();
                    }
                    : null,
          ),

          const SizedBox(height: 16),

          // 具体条件设置
          if (_conditions['type'] == 'POINTS_DONATED')
            _buildPointsDonatedCondition(),

          if (_conditions['type'] == 'LEVEL_REQUIRED')
            _buildLevelRequiredCondition(),

          if (_conditions['type'] == 'PAY_TO_VIEW') _buildPayToViewCondition(),
        ],
      ),
    );
  }

  /// 构建积分赠送条件设置
  Widget _buildPointsDonatedCondition() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          decoration: const InputDecoration(
            labelText: '最少赠送积分',
            border: OutlineInputBorder(),
            suffixText: '积分',
            helperText: '用户需要向您赠送的最少积分数',
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          keyboardType: TextInputType.number,
          initialValue: _conditions['minPoints']?.toString() ?? '',
          enabled: widget.enabled,
          onChanged: (value) {
            final points = int.tryParse(value) ?? 0;
            setState(() {
              _conditions['minPoints'] = points;
            });
            _notifyChange();
          },
        ),

        const SizedBox(height: 8),

        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.amber.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.amber.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.amber[700], size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '用户向您赠送达到指定积分后，即可查看此钓点',
                  style: TextStyle(fontSize: 12, color: Colors.amber[700]),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建等级要求条件设置
  Widget _buildLevelRequiredCondition() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          decoration: const InputDecoration(
            labelText: '最低等级要求',
            border: OutlineInputBorder(),
            suffixText: '级',
            helperText: '用户需要达到的最低等级',
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          keyboardType: TextInputType.number,
          initialValue: _conditions['minLevel']?.toString() ?? '',
          enabled: widget.enabled,
          onChanged: (value) {
            final level = int.tryParse(value) ?? 1;
            setState(() {
              _conditions['minLevel'] = level;
            });
            _notifyChange();
          },
        ),

        const SizedBox(height: 8),

        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.purple.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.purple.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.purple[700], size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '用户等级达到要求后，即可查看此钓点（等级 = 积分 ÷ 1000 + 1）',
                  style: TextStyle(fontSize: 12, color: Colors.purple[700]),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建付费查看条件设置
  Widget _buildPayToViewCondition() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          decoration: const InputDecoration(
            labelText: '查看费用',
            border: OutlineInputBorder(),
            suffixText: '积分',
            helperText: '用户需要支付的积分数量',
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          keyboardType: TextInputType.number,
          initialValue: _conditions['price']?.toString() ?? '',
          enabled: widget.enabled,
          onChanged: (value) {
            final price = int.tryParse(value) ?? 0;
            setState(() {
              _conditions['price'] = price;
            });
            _notifyChange();
          },
        ),

        const SizedBox(height: 8),

        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.green.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.green[700], size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '用户支付指定积分后，即可在地图上查看此钓点',
                  style: TextStyle(fontSize: 12, color: Colors.green[700]),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 通知可见性变更
  void _notifyChange() {
    final conditions =
        _selectedVisibility == SpotVisibility.conditional
            ? Map<String, dynamic>.from(_conditions)
            : null;

    widget.onChanged(_selectedVisibility, conditions);
  }
}

/// 钓点可见性指示器组件
///
/// 用于在钓点列表中显示可见性状态
class SpotVisibilityIndicator extends StatelessWidget {
  final SpotVisibility visibility;
  final double size;
  final bool showLabel;

  const SpotVisibilityIndicator({
    Key? key,
    required this.visibility,
    this.size = 16,
    this.showLabel = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    IconData iconData;
    Color color;

    switch (visibility) {
      case SpotVisibility.private:
        iconData = Icons.lock;
        color = Colors.red;
        break;
      case SpotVisibility.friendsOnly:
        iconData = Icons.people;
        color = Colors.orange;
        break;
      case SpotVisibility.conditional:
        iconData = Icons.star;
        color = Colors.blue;
        break;
      case SpotVisibility.public:
        iconData = Icons.public;
        color = Colors.green;
        break;
    }

    if (showLabel) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(iconData, color: color, size: size),
          const SizedBox(width: 4),
          Text(
            visibility.displayName,
            style: TextStyle(
              color: color,
              fontSize: size * 0.8,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    }

    return Tooltip(
      message: visibility.displayName,
      child: Icon(iconData, color: color, size: size),
    );
  }
}

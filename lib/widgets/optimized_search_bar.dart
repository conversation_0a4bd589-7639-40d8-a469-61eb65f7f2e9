import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;
import 'dart:async';
import '../utils/tianditu_utils.dart';

/// 优化后的搜索组件
/// 默认周边搜索200公里，按距离排序显示结果
class OptimizedSearchBar extends StatefulWidget {
  final Function(LatLng) onLocationSelected;
  final VoidCallback? onSearchStarted;
  final VoidCallback? onSearchEnded;
  final LatLng? currentLocation;
  final String? hintText;

  const OptimizedSearchBar({
    super.key,
    required this.onLocationSelected,
    this.onSearchStarted,
    this.onSearchEnded,
    this.currentLocation,
    this.hintText = '搜索地点、地址',
  });

  @override
  State<OptimizedSearchBar> createState() => _OptimizedSearchBarState();
}

class _OptimizedSearchBarState extends State<OptimizedSearchBar> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  List<_SearchResultWithDistance> _searchResults = [];
  bool _isSearching = false;
  String? _errorMessage;

  // 搜索范围常量
  static const int _nearbyRadius = 200000; // 200公里，单位：米

  // 搜索取消和防抖
  Timer? _debounceTimer;
  String? _currentSearchQuery;
  
  @override
  void initState() {
    super.initState();
    _setupFocusListener();
  }

  void _setupFocusListener() {
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        widget.onSearchStarted?.call();
      } else {
        widget.onSearchEnded?.call();
      }
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 执行周边搜索
  Future<void> _performNearbySearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _errorMessage = null;
      });
      return;
    }

    // 检查是否是当前查询，避免重复搜索
    if (_currentSearchQuery == query && _isSearching) {
      return;
    }

    _currentSearchQuery = query;
    setState(() {
      _isSearching = true;
      _errorMessage = null;
    });

    try {
      List<SearchResult> results = [];

      if (widget.currentLocation != null) {
        // 使用普通搜索配合地图边界实现200公里范围搜索
        final mapBound = _calculateMapBound(widget.currentLocation!, _nearbyRadius / 1000); // 转换为公里
        results = await TianDiTuUtils.searchByName(
          query,
          queryType: 1, // 普通搜索
          mapBound: mapBound,
          count: 100, // 天地图API实际最大支持100条
        ) ?? [];

        // 如果搜索结果包含区域结果，尝试用城市名+热门关键词重新搜索获取POI
        if (results.any((result) => result.category == '行政区')) {
          debugPrint('检测到区域结果，使用POI搜索策略重新搜索');

          // 使用城市名+热门关键词进行POI搜索
          final poiResults = await TianDiTuUtils.searchByName(
            '$query 景点',
            queryType: 1, // 普通搜索
            mapBound: mapBound,
            count: 50,
          ) ?? [];

          final poiResults2 = await TianDiTuUtils.searchByName(
            '$query 商场',
            queryType: 1, // 普通搜索
            mapBound: mapBound,
            count: 50,
          ) ?? [];

          // 合并POI结果，去重
          final allPoiResults = <SearchResult>[];
          final seenNames = <String>{};

          for (final result in [...poiResults, ...poiResults2]) {
            if (result.category != '行政区' && !seenNames.contains(result.name)) {
              allPoiResults.add(result);
              seenNames.add(result.name);
            }
          }

          if (allPoiResults.isNotEmpty) {
            results = allPoiResults;
            debugPrint('POI搜索结果: ${results.length} 条');
          }
        }

        // 过滤200公里范围内的结果
        if (results.isNotEmpty) {
          results = results.where((result) {
            final distance = _calculateDistance(
              widget.currentLocation!.latitude,
              widget.currentLocation!.longitude,
              result.latitude,
              result.longitude,
            );
            return distance <= (_nearbyRadius / 1000); // 转换为公里进行比较
          }).toList();
        }

        debugPrint('200公里范围内搜索结果: ${results.length} 条');
      } else {
        // 没有位置信息时使用普通搜索
        results = await TianDiTuUtils.searchByName(
          query,
          queryType: 1, // 普通搜索
          count: 100, // 天地图API实际最大支持100条
        ) ?? [];

        // 如果搜索结果包含区域结果，尝试用城市名+热门关键词重新搜索获取POI
        if (results.any((result) => result.category == '行政区')) {
          debugPrint('检测到区域结果，使用POI搜索策略重新搜索');

          // 使用城市名+热门关键词进行POI搜索
          final poiResults = await TianDiTuUtils.searchByName(
            '$query 景点',
            queryType: 1, // 普通搜索
            count: 50,
          ) ?? [];

          final poiResults2 = await TianDiTuUtils.searchByName(
            '$query 商场',
            queryType: 1, // 普通搜索
            count: 50,
          ) ?? [];

          // 合并POI结果，去重
          final allPoiResults = <SearchResult>[];
          final seenNames = <String>{};

          for (final result in [...poiResults, ...poiResults2]) {
            if (result.category != '行政区' && !seenNames.contains(result.name)) {
              allPoiResults.add(result);
              seenNames.add(result.name);
            }
          }

          if (allPoiResults.isNotEmpty) {
            results = allPoiResults;
            debugPrint('POI搜索结果: ${results.length} 条');
          }
        }

        debugPrint('普通搜索结果: ${results.length} 条');
      }

      // 检查查询是否仍然有效（用户可能已经输入了新的查询）
      if (mounted && _currentSearchQuery == query) {
        if (results.isNotEmpty) {
          // 计算距离并排序
          final resultsWithDistance = _calculateDistances(results);
          setState(() {
            _searchResults = resultsWithDistance;
            _isSearching = false;
            _errorMessage = null;
          });
        } else {
          // 没有搜索结果
          setState(() {
            _searchResults = [];
            _isSearching = false;
            _errorMessage = null;
          });
        }
      }
    } catch (e) {
      if (mounted && _currentSearchQuery == query) {
        setState(() {
          _searchResults = [];
          _isSearching = false;
          _errorMessage = '搜索失败，请检查网络连接后重试';
        });
      }
      debugPrint('搜索失败: $e');
    }
  }

  /// 计算地图边界（基于中心点和半径）
  String _calculateMapBound(LatLng center, double radiusKm) {
    // 计算纬度偏移（1度纬度约等于111公里）
    final double latOffset = radiusKm / 111.0;
    
    // 计算经度偏移（考虑纬度影响）
    final double lonOffset = radiusKm / (111.0 * math.cos(center.latitude * math.pi / 180));
    
    final double minLon = center.longitude - lonOffset;
    final double maxLon = center.longitude + lonOffset;
    final double minLat = center.latitude - latOffset;
    final double maxLat = center.latitude + latOffset;
    
    // 确保边界值在有效范围内
    final double boundedMinLon = math.max(-180, minLon);
    final double boundedMaxLon = math.min(180, maxLon);
    final double boundedMinLat = math.max(-90, minLat);
    final double boundedMaxLat = math.min(90, maxLat);
    
    final mapBound = '$boundedMinLon,$boundedMinLat,$boundedMaxLon,$boundedMaxLat';
    debugPrint('计算的地图边界: $mapBound (半径: ${radiusKm}km)');
    
    return mapBound;
  }

  /// 计算距离并排序
  List<_SearchResultWithDistance> _calculateDistances(List<SearchResult> results) {
    if (widget.currentLocation == null) {
      return results.map((result) => _SearchResultWithDistance(result, null)).toList();
    }

    final resultsWithDistance = results.map((result) {
      final distance = _calculateDistance(
        widget.currentLocation!.latitude,
        widget.currentLocation!.longitude,
        result.latitude,
        result.longitude,
      );
      return _SearchResultWithDistance(result, distance);
    }).toList();

    // 按距离排序
    resultsWithDistance.sort((a, b) {
      if (a.distance == null && b.distance == null) return 0;
      if (a.distance == null) return 1;
      if (b.distance == null) return -1;
      return a.distance!.compareTo(b.distance!);
    });

    return resultsWithDistance;
  }

  /// 计算距离（公里）
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // 地球半径，单位：公里
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) *
        math.sin(dLon / 2) * math.sin(dLon / 2);
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) => degrees * (math.pi / 180);

  /// 格式化距离显示
  String _formatDistance(double? distance) {
    if (distance == null) return '';
    if (distance < 1) {
      return '${(distance * 1000).round()}m';
    } else {
      return '${distance.toStringAsFixed(1)}km';
    }
  }


  /// 清除搜索内容
  void _clearSearch() {
    _debounceTimer?.cancel();
    _currentSearchQuery = null;
    _controller.clear();
    setState(() {
      _searchResults = [];
      _errorMessage = null;
      _isSearching = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // [*调整参数*]搜索栏
        Container(
          margin: EdgeInsets.only(
            top: MediaQuery.of(context).padding.top - 20, // 往上移10像素
            left: 16,
            right: 16,
          ),
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(10),
            shadowColor: Colors.black.withValues(alpha: 0.1),
            child: Container(
              height: 50,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                textAlign: TextAlign.center,
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 16,
                  ),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.all(16),
                    child: _isSearching
                        ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.grey[400]!,
                              ),
                            ),
                          )
                        : FaIcon(
                            FontAwesomeIcons.magnifyingGlass,
                            size: 20,
                            color: Colors.grey[400],
                          ),
                  ),
                  suffixIcon: _controller.text.isNotEmpty
                      ? IconButton(
                          icon: const FaIcon(FontAwesomeIcons.xmark, size: 16),
                          onPressed: _clearSearch,
                          color: Colors.grey[400],
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                ),
                style: const TextStyle(fontSize: 16),
                onChanged: (value) {
                  // 取消之前的防抖计时器
                  _debounceTimer?.cancel();

                  if (value.trim().isNotEmpty) {
                    // 统一防抖时间为400ms
                    _debounceTimer = Timer(const Duration(milliseconds: 400), () {
                      if (_controller.text == value && mounted) {
                        _performNearbySearch(value);
                      }
                    });
                  } else {
                    _clearSearch();
                  }
                },
                onSubmitted: (value) {
                  if (value.trim().isNotEmpty) {
                    _performSearchAndSelectFirst(value);
                  }
                },
              ),
            ),
          ),
        ),
        
        // 搜索结果、错误提示或空状态
        if (_errorMessage != null || _searchResults.isNotEmpty || (_controller.text.isNotEmpty && !_isSearching))
          Flexible(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.5, // 最大高度为屏幕高度的50%
              ),
              child: _buildSearchContent(),
            ),
          ),
      ],
    );
  }

  /// 构建搜索内容（结果、错误或空状态）
  Widget _buildSearchContent() {
    // 显示错误信息
    if (_errorMessage != null) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FontAwesomeIcons.triangleExclamation,
              size: 32,
              color: Colors.orange[600],
            ),
            const SizedBox(height: 12),
            Text(
              _errorMessage!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            TextButton(
              onPressed: () {
                if (_controller.text.isNotEmpty) {
                  _performNearbySearch(_controller.text);
                }
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    // 显示搜索结果
    if (_searchResults.isNotEmpty) {
      return ListView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        itemCount: _searchResults.length,
        itemBuilder: (context, index) {
          final resultWithDistance = _searchResults[index];
          final result = resultWithDistance.result;
          final distance = resultWithDistance.distance;

          return ListTile(
            dense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            minVerticalPadding: 0,
            leading: Icon(
              FontAwesomeIcons.locationDot,
              size: 16,
              color: Theme.of(context).primaryColor,
            ),
            title: Text(
              result.name,
              style: const TextStyle(fontSize: 14),
            ),
            subtitle: Text(
              result.address,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: distance != null
                ? Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _formatDistance(distance),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )
                : null,
            onTap: () => _selectResult(result),
          );
        },
      );
    }

    // 显示空状态（用户输入了内容但没有结果）
    // 没找到结果时不显示任何内容
    return const SizedBox.shrink();
  }

  /// 选择搜索结果
  void _selectResult(SearchResult result) {
    debugPrint('选择搜索结果: ${result.name}, 坐标: (${result.longitude}, ${result.latitude})');

    _controller.clear(); // 清空搜索框
    _focusNode.unfocus();

    // 清除所有搜索状态
    setState(() {
      _searchResults = [];
      _errorMessage = null;
      _isSearching = false;
    });

    // 检查坐标是否有效
    if (result.latitude != 0.0 || result.longitude != 0.0) {
      widget.onLocationSelected(LatLng(result.latitude, result.longitude));
    } else {
      debugPrint('⚠️ 坐标无效，无法移动地图');
    }
  }

  /// 执行搜索并选择第一个结果
  Future<void> _performSearchAndSelectFirst(String query) async {
    await _performNearbySearch(query);
    
    // 如果有搜索结果，自动选择第一个
    if (_searchResults.isNotEmpty) {
      final firstResult = _searchResults.first.result;
      debugPrint('选择搜索结果: ${firstResult.name}, 坐标: (${firstResult.longitude}, ${firstResult.latitude})');
      
      _controller.text = firstResult.name;
      _focusNode.unfocus();
      
      // 检查坐标是否有效
      if (firstResult.latitude != 0.0 || firstResult.longitude != 0.0) {
        widget.onLocationSelected(LatLng(firstResult.latitude, firstResult.longitude));
      } else {
        debugPrint('⚠️ 坐标无效，无法移动地图');
      }
    } else {
      debugPrint('⚠️ 没有搜索结果');
    }
  }

}

/// 搜索结果与距离的组合类
class _SearchResultWithDistance {
  final SearchResult result;
  final double? distance; // 距离，单位：公里

  _SearchResultWithDistance(this.result, this.distance);
}
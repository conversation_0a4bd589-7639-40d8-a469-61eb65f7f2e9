import 'package:flutter/foundation.dart';
import '../config/pocketbase_config.dart';

/// 数据库迁移辅助工具
///
/// 用于检查和修复数据库结构，确保付费查看功能正常工作
class DatabaseMigrationHelper {
  static final PocketBaseConfig _pb = PocketBaseConfig.instance;

  /// 检查数据库结构是否支持付费查看功能
  static Future<bool> checkDatabaseStructure() async {
    try {
      debugPrint('🔍 [数据库检查] 开始检查数据库结构...');

      // 检查 fishing_spots 集合是否有 visibility 字段
      final hasVisibilityField = await _checkFishingSpotsStructure();

      // 检查 user_point_donations 集合是否存在
      final hasPointDonationsTable = await _checkUserPointDonationsStructure();

      // 检查 user_follows 集合是否存在
      final hasUserFollowsTable = await _checkUserFollowsStructure();

      final isComplete =
          hasVisibilityField && hasPointDonationsTable && hasUserFollowsTable;

      debugPrint('🔍 [数据库检查] 检查结果:');
      debugPrint(
        '  - fishing_spots.visibility: ${hasVisibilityField ? "✅" : "❌"}',
      );
      debugPrint(
        '  - user_point_donations: ${hasPointDonationsTable ? "✅" : "❌"}',
      );
      debugPrint('  - user_follows: ${hasUserFollowsTable ? "✅" : "❌"}');
      debugPrint('  - 整体状态: ${isComplete ? "✅ 完整" : "❌ 需要修复"}');

      return isComplete;
    } catch (e) {
      debugPrint('❌ [数据库检查] 检查失败: $e');
      return false;
    }
  }

  /// 检查 fishing_spots 集合结构
  static Future<bool> _checkFishingSpotsStructure() async {
    try {
      // 尝试查询一个钓点记录，检查是否有 visibility 字段
      final result = await _pb.client
          .collection('fishing_spots')
          .getList(page: 1, perPage: 1);

      if (result.items.isNotEmpty) {
        final firstItem = result.items.first;
        final hasVisibility = firstItem.data.containsKey('visibility');
        final hasVisibilityConditions = firstItem.data.containsKey(
          'visibility_conditions',
        );

        debugPrint('🔍 [数据库检查] fishing_spots 字段检查:');
        debugPrint('  - visibility: ${hasVisibility ? "存在" : "缺失"}');
        debugPrint(
          '  - visibility_conditions: ${hasVisibilityConditions ? "存在" : "缺失"}',
        );

        return hasVisibility;
      } else {
        debugPrint('🔍 [数据库检查] fishing_spots 集合为空，无法检查字段');
        return true; // 空集合认为是正常的
      }
    } catch (e) {
      debugPrint('❌ [数据库检查] 检查 fishing_spots 失败: $e');
      return false;
    }
  }

  /// 检查 user_point_donations 集合结构
  static Future<bool> _checkUserPointDonationsStructure() async {
    try {
      await _pb.client
          .collection('user_point_donations')
          .getList(page: 1, perPage: 1);

      debugPrint('✅ [数据库检查] user_point_donations 集合存在');
      return true;
    } catch (e) {
      debugPrint('❌ [数据库检查] user_point_donations 集合不存在: $e');
      return false;
    }
  }

  /// 检查 user_follows 集合结构
  static Future<bool> _checkUserFollowsStructure() async {
    try {
      await _pb.client.collection('user_follows').getList(page: 1, perPage: 1);

      debugPrint('✅ [数据库检查] user_follows 集合存在');
      return true;
    } catch (e) {
      final errorStr = e.toString();
      if (errorStr.contains('403') || errorStr.contains('Only superusers')) {
        debugPrint('⚠️ [数据库检查] user_follows 集合存在但权限不足（这是正常的）');
        return true; // 权限不足但集合存在，认为是正常的
      }
      debugPrint('❌ [数据库检查] user_follows 集合不存在: $e');
      return false;
    }
  }

  /// 获取数据库修复建议
  static List<String> getDatabaseFixSuggestions() {
    return [
      '1. 在 PocketBase Admin UI 中，找到 fishing_spots 集合',
      '2. 添加以下字段：',
      '   - visibility (Select): 选项为 PUBLIC, PRIVATE, FRIENDS_ONLY, CONDITIONAL',
      '   - visibility_conditions (JSON): 可选字段',
      '   - visibility_updated_at (Date): 默认值为 @now',
      '3. 创建 user_point_donations 集合，包含字段：',
      '   - donor_id (Relation to users)',
      '   - recipient_id (Relation to users)',
      '   - points (Number)',
      '   - reason (Text, 可选)',
      '   - spot_id (Relation to fishing_spots, 可选)',
      '4. 创建 user_follows 集合，包含字段：',
      '   - follower_id (Relation to users)',
      '   - following_id (Relation to users)',
      '5. 重启应用以应用更改',
    ];
  }

  /// 显示数据库修复指南
  static void showDatabaseFixGuide() {
    debugPrint('📋 [数据库修复] 修复指南:');
    final suggestions = getDatabaseFixSuggestions();
    for (final suggestion in suggestions) {
      debugPrint('   $suggestion');
    }
  }

  /// 测试基本的数据库连接
  static Future<bool> testDatabaseConnection() async {
    try {
      debugPrint('🔍 [数据库测试] 测试基本连接...');

      // 尝试获取用户列表
      await _pb.client.collection('users').getList(page: 1, perPage: 1);

      debugPrint('✅ [数据库测试] 数据库连接正常');
      return true;
    } catch (e) {
      debugPrint('❌ [数据库测试] 数据库连接失败: $e');
      return false;
    }
  }

  /// 执行完整的数据库健康检查
  static Future<Map<String, dynamic>> performHealthCheck() async {
    final results = <String, dynamic>{};

    try {
      // 基本连接测试
      results['connection'] = await testDatabaseConnection();

      // 结构检查
      results['structure'] = await checkDatabaseStructure();

      // 详细检查结果
      results['details'] = {
        'fishing_spots_visibility': await _checkFishingSpotsStructure(),
        'user_point_donations': await _checkUserPointDonationsStructure(),
        'user_follows': await _checkUserFollowsStructure(),
      };

      // 修复建议
      if (!results['structure']) {
        results['fix_suggestions'] = getDatabaseFixSuggestions();
      }

      debugPrint('🏥 [数据库健康检查] 完成');
      debugPrint('   连接状态: ${results['connection'] ? "正常" : "异常"}');
      debugPrint('   结构完整性: ${results['structure'] ? "完整" : "不完整"}');
    } catch (e) {
      debugPrint('❌ [数据库健康检查] 检查失败: $e');
      results['error'] = e.toString();
    }

    return results;
  }
}

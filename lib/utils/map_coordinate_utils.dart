import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

/// 地图坐标转换工具类
/// 提供屏幕坐标与地理坐标之间的精确转换
class MapCoordinateUtils {
  /// 将指定地理坐标移动到屏幕的指定位置
  ///
  /// [mapController] 地图控制器
  /// [targetCoordinate] 目标地理坐标
  /// [screenHeightPercent] 屏幕高度百分比 (0.0 - 1.0)
  /// [screenWidthPercent] 屏幕宽度百分比 (0.0 - 1.0)
  /// [screenSize] 屏幕尺寸
  static void moveCoordinateToScreenPosition(
    MapController mapController,
    LatLng targetCoordinate, {
    required double screenHeightPercent,
    required double screenWidthPercent,
    required Size screenSize,
  }) {
    final camera = mapController.camera;

    // 计算目标屏幕位置
    final targetScreenX = screenSize.width * screenWidthPercent;
    final targetScreenY = screenSize.height * screenHeightPercent;
    final targetScreenOffset = Offset(targetScreenX, targetScreenY);

    debugPrint('🔍 [地图移动] 屏幕尺寸: ${screenSize.width} x ${screenSize.height}');
    debugPrint(
      '🔍 [地图移动] 目标屏幕位置: ${(screenHeightPercent * 100).toInt()}%高度, ${(screenWidthPercent * 100).toInt()}%宽度',
    );
    debugPrint('🔍 [地图移动] 目标屏幕像素: ($targetScreenX, $targetScreenY)');
    debugPrint(
      '🔍 [地图移动] 目标坐标: ${targetCoordinate.latitude}, ${targetCoordinate.longitude}',
    );

    // 使用Flutter Map官方API将目标坐标转换为当前屏幕位置
    final currentScreenOffset = camera.latLngToScreenOffset(targetCoordinate);
    debugPrint(
      '🔍 [地图移动] 目标坐标当前屏幕位置: (${currentScreenOffset.dx}, ${currentScreenOffset.dy})',
    );

    // 计算需要移动的屏幕像素偏移
    // 注意：要让目标坐标从当前位置移动到目标位置，地图需要向相反方向移动
    final screenOffsetDelta = currentScreenOffset - targetScreenOffset;
    debugPrint(
      '🔍 [地图移动] 需要移动的屏幕偏移: (${screenOffsetDelta.dx}, ${screenOffsetDelta.dy})',
    );

    // 计算屏幕中心位置
    final screenCenter = Offset(screenSize.width / 2, screenSize.height / 2);

    // 计算新的屏幕中心位置
    // 地图中心需要向相反方向移动，所以是加法
    final newScreenCenter = screenCenter + screenOffsetDelta;

    // 使用Flutter Map官方API将新的屏幕中心位置转换为地理坐标
    final newMapCenter = camera.screenOffsetToLatLng(newScreenCenter);

    debugPrint(
      '🔍 [地图移动] 新地图中心: ${newMapCenter.latitude}, ${newMapCenter.longitude}',
    );

    // 移动地图到新位置
    mapController.move(newMapCenter, camera.zoom);
  }

  /// 计算屏幕指定位置对应的地理坐标
  ///
  /// [camera] 地图相机
  /// [screenHeightPercent] 屏幕高度百分比 (0.0 - 1.0)
  /// [screenWidthPercent] 屏幕宽度百分比 (0.0 - 1.0)
  /// [screenSize] 屏幕尺寸
  static LatLng calculateLocationAtScreenPosition(
    MapCamera camera, {
    required double screenHeightPercent,
    required double screenWidthPercent,
    required Size screenSize,
  }) {
    // 目标屏幕位置
    final targetScreenOffset = Offset(
      screenSize.width * screenWidthPercent,
      screenSize.height * screenHeightPercent,
    );

    // 使用Flutter Map官方API将屏幕位置转换为地理坐标
    return camera.screenOffsetToLatLng(targetScreenOffset);
  }

  /// 计算屏幕25%高度、50%宽度处对应的地理坐标
  /// 这是分屏模式下中心标记的位置
  static LatLng calculateLocationAt25PercentHeight(
    MapCamera camera,
    Size screenSize,
  ) {
    return calculateLocationAtScreenPosition(
      camera,
      screenHeightPercent: 0.25,
      screenWidthPercent: 0.50,
      screenSize: screenSize,
    );
  }

  /// 验证坐标转换的准确性
  /// 用于调试和测试
  static void validateCoordinateConversion(
    MapCamera camera,
    LatLng originalCoordinate,
    Size screenSize,
  ) {
    // 将地理坐标转换为屏幕坐标
    final screenOffset = camera.latLngToScreenOffset(originalCoordinate);

    // 再将屏幕坐标转换回地理坐标
    final convertedCoordinate = camera.screenOffsetToLatLng(screenOffset);

    // 计算误差
    final latError =
        (originalCoordinate.latitude - convertedCoordinate.latitude).abs();
    final lngError =
        (originalCoordinate.longitude - convertedCoordinate.longitude).abs();

    debugPrint(
      '🔍 [坐标转换验证] 原始坐标: ${originalCoordinate.latitude}, ${originalCoordinate.longitude}',
    );
    debugPrint('🔍 [坐标转换验证] 屏幕坐标: (${screenOffset.dx}, ${screenOffset.dy})');
    debugPrint(
      '🔍 [坐标转换验证] 转换后坐标: ${convertedCoordinate.latitude}, ${convertedCoordinate.longitude}',
    );
    debugPrint(
      '🔍 [坐标转换验证] 误差: 纬度${latError.toStringAsFixed(10)}, 经度${lngError.toStringAsFixed(10)}',
    );

    // 如果误差过大，输出警告
    if (latError > 1e-6 || lngError > 1e-6) {
      debugPrint('⚠️ [坐标转换验证] 警告：坐标转换误差较大！');
    }
  }

  /// 检查屏幕位置是否在有效范围内
  static bool isScreenPositionValid(Offset screenOffset, Size screenSize) {
    return screenOffset.dx >= 0 &&
        screenOffset.dx <= screenSize.width &&
        screenOffset.dy >= 0 &&
        screenOffset.dy <= screenSize.height;
  }

  /// 将屏幕百分比位置转换为像素位置
  static Offset percentToPixel(
    double widthPercent,
    double heightPercent,
    Size screenSize,
  ) {
    return Offset(
      screenSize.width * widthPercent,
      screenSize.height * heightPercent,
    );
  }

  /// 将像素位置转换为屏幕百分比位置
  static Offset pixelToPercent(Offset pixelOffset, Size screenSize) {
    return Offset(
      pixelOffset.dx / screenSize.width,
      pixelOffset.dy / screenSize.height,
    );
  }
}

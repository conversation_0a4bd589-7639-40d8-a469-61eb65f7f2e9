import 'package:flutter/material.dart';

/// Marker对齐工具类
/// 用于精确计算地图标记的对齐方式，确保图标尖点准确对准坐标点
class MarkerAlignmentUtils {
  /// 计算钓点标记的精确对齐方式
  ///
  /// 确保钓点图标的尖点底部精确对准地理坐标点
  ///
  /// [markerSize] 标记的基础尺寸
  /// [markerWidth] Marker的宽度
  /// [markerHeight] Marker的高度
  ///
  /// 返回Alignment对象，用于Marker的alignment属性
  static Alignment calculateFishingSpotAlignment({
    double markerSize = 60.0,
    double markerWidth = 80.0,
    double markerHeight = 96.0,
  }) {
    // 钓点图标的内部结构计算
    final iconHeight = markerSize * 1.2; // 图标总高度
    final circleRadius = markerSize * 0.35; // 圆形半径
    final triangleHeight = markerSize * 0.25; // 三角形高度

    // 计算尖点底部在图标内的位置
    final circleTop = markerSize * 0.05; // 圆形顶部偏移
    final circleCenter = circleTop + circleRadius; // 圆心位置
    final triangleTop = circleCenter + circleRadius; // 三角形顶部
    final triangleBottom = triangleTop + triangleHeight; // 三角形底部（尖点）

    // 计算尖点在Marker中的相对位置
    // Marker的中心点在 (markerWidth/2, markerHeight/2)
    // 尖点位置在 (markerWidth/2, triangleBottom + (markerHeight - iconHeight)/2)

    final markerCenterY = markerHeight / 2;
    final iconOffsetY = (markerHeight - iconHeight) / 2; // 图标在Marker中的垂直偏移
    final tipPositionY = triangleBottom + iconOffsetY; // 尖点在Marker中的绝对位置

    // 计算alignment的y值
    // alignment.y = -1 表示顶部，1 表示底部，0 表示中心
    // 我们需要让尖点对准坐标点，所以计算尖点相对于Marker中心的偏移
    final alignmentY = (tipPositionY - markerCenterY) / (markerHeight / 2);

    debugPrint('🔍 [Marker对齐] 计算参数:');
    debugPrint('🔍 [Marker对齐] - 标记尺寸: $markerSize');
    debugPrint('🔍 [Marker对齐] - Marker尺寸: $markerWidth x $markerHeight');
    debugPrint('🔍 [Marker对齐] - 图标高度: $iconHeight');
    debugPrint('🔍 [Marker对齐] - 圆心位置: $circleCenter');
    debugPrint('🔍 [Marker对齐] - 尖点位置: $triangleBottom');
    debugPrint('🔍 [Marker对齐] - Marker中心: $markerCenterY');
    debugPrint('🔍 [Marker对齐] - 尖点在Marker中位置: $tipPositionY');
    debugPrint('🔍 [Marker对齐] - 计算的alignment.y: $alignmentY');

    return Alignment(0.0, alignmentY);
  }

  /// 验证对齐计算的准确性
  ///
  /// 用于调试和测试，确保尖点位置计算正确
  static void validateAlignment({
    double markerSize = 60.0,
    double markerWidth = 80.0,
    double markerHeight = 96.0,
  }) {
    final alignment = calculateFishingSpotAlignment(
      markerSize: markerSize,
      markerWidth: markerWidth,
      markerHeight: markerHeight,
    );

    // 验证计算结果
    if (alignment.y < -1.0 || alignment.y > 1.0) {
      debugPrint('⚠️ [Marker对齐] 警告：计算的alignment.y值超出有效范围: ${alignment.y}');
    } else {
      debugPrint('✅ [Marker对齐] 对齐计算验证通过: $alignment');
    }
  }

  /// 获取推荐的Marker尺寸
  ///
  /// 基于图标尺寸计算推荐的Marker宽度和高度
  static Size getRecommendedMarkerSize(double iconSize) {
    final width = iconSize * 1.33; // 留出一些边距
    final height = iconSize * 1.6; // 为尖点留出足够空间

    return Size(width, height);
  }

  /// 计算图钉📍的精确偏移
  ///
  /// 用于分屏模式下的图钉定位，确保图钉针尖与钓点图标尖点对齐
  ///
  /// [screenHeight] 屏幕高度
  /// [targetHeightPercent] 目标高度百分比（如0.25表示25%）
  /// [pinSize] 图钉的字体大小
  static double calculatePinOffset({
    required double screenHeight,
    required double targetHeightPercent,
    double pinSize = 40.0,
  }) {
    // 图钉📍的针尖大约在字符高度的85%位置
    final pinTipOffset = pinSize * 1.25;

    // 计算让针尖对准目标位置的top偏移
    final targetPosition = screenHeight * targetHeightPercent;
    final pinTopOffset = targetPosition - pinTipOffset;

    // debugPrint('🔍 [图钉对齐] 屏幕高度: $screenHeight');
    // debugPrint('🔍 [图钉对齐] 目标位置: $targetPosition');
    // debugPrint('🔍 [图钉对齐] 图钉尺寸: $pinSize');
    // debugPrint('🔍 [图钉对齐] 针尖偏移: $pinTipOffset');
    // debugPrint('🔍 [图钉对齐] 计算的top偏移: $pinTopOffset');

    return pinTopOffset;
  }

  /// 创建精确对齐的钓点Marker
  ///
  /// 返回配置好的Marker参数，确保尖点精确对准坐标点
  static Map<String, dynamic> createAlignedFishingSpotMarker({
    double iconSize = 60.0,
  }) {
    final markerSize = getRecommendedMarkerSize(iconSize);
    final alignment = calculateFishingSpotAlignment(
      markerSize: iconSize,
      markerWidth: markerSize.width,
      markerHeight: markerSize.height,
    );

    return {
      'width': markerSize.width,
      'height': markerSize.height,
      'alignment': alignment,
      'iconSize': iconSize,
    };
  }
}

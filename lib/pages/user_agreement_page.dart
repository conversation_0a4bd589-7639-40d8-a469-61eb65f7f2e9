import 'package:flutter/material.dart';

/// 用户协议页面
class UserAgreementPage extends StatelessWidget {
  const UserAgreementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '用户服务协议',
          style: TextStyle(color: Colors.black, fontWeight: FontWeight.w600),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Text(
              '鱼一窝用户服务协议',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF333333),
              ),
            ),
            SizedBox(height: 8),
            Text(
              '生效日期：2024年1月1日',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
              ),
            ),
            SizedBox(height: 20),
            
            // 协议内容
            _AgreementSection(
              title: '1. 服务简介',
              content: '鱼一窝是一款专为钓鱼爱好者打造的社交分享平台，提供钓点分享、钓鱼记录、社区交流等功能。',
            ),
            
            _AgreementSection(
              title: '2. 用户注册与账户',
              content: '''2.1 用户需提供真实、准确的注册信息
2.2 用户有责任保护账户安全，不得将账户转让给他人
2.3 发现账户被盗用应立即联系客服''',
            ),
            
            _SafetyWarningSection(),
            
            _AgreementSection(
              title: '4. 用户行为规范',
              content: '''4.1 不得发布违法、有害、虚假信息
4.2 不得侵犯他人知识产权或隐私权
4.3 不得恶意刷屏、发布垃圾信息
4.4 尊重其他用户，维护良好的社区氛围
4.5 分享钓点信息时应确保准确性，不得故意误导他人''',
            ),
            
            _AgreementSection(
              title: '5. 免责声明',
              content: '''5.1 钓鱼活动风险：用户参与钓鱼活动的风险由用户自行承担
5.2 信息准确性：平台不保证用户分享信息的完全准确性
5.3 服务中断：因技术故障等原因导致的服务中断，平台不承担责任''',
            ),
            
            _AgreementSection(
              title: '6. 联系我们',
              content: '''如有疑问或建议，请联系我们：
• 邮箱：<EMAIL>
• 客服热线：400-XXX-XXXX''',
            ),
            
            SizedBox(height: 20),
            
            // 重要提醒
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Color(0xFFFFF3E0),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Color(0xFFFF9800), width: 1),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: Color(0xFFFF9800),
                    size: 32,
                  ),
                  SizedBox(height: 8),
                  Text(
                    '重要提醒：钓鱼有风险，安全需谨慎！',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFFFF9800),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 40),
          ],
        ),
      ),
    );
  }
}

/// 协议章节组件
class _AgreementSection extends StatelessWidget {
  final String title;
  final String content;
  
  const _AgreementSection({
    required this.title,
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF666666),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}

/// 安全警示章节（特殊样式）
class _SafetyWarningSection extends StatelessWidget {
  const _SafetyWarningSection();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFFEBEE),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE57373), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security,
                color: const Color(0xFFE57373),
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                '3. ⚠️ 安全警示（重要）',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFE57373),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            '野外钓鱼安全注意事项：',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '''• 人身安全第一：建议结伴出行，选择安全钓点
• 水域安全：注意水深水流，携带安全设备
• 环境安全：防范野生动物，夜钓注意照明
• 健康安全：携带药品，注意防晒防虫

本平台仅提供信息分享服务，用户应自行判断钓点安全性，平台不承担钓鱼活动产生的安全责任。''',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF666666),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}

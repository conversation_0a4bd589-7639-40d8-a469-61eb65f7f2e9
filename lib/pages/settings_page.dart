import 'package:flutter/material.dart';
import '../widgets/profile_widgets.dart';

/// 设置页面
class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool _notificationsEnabled = true;
  bool _locationEnabled = true;
  bool _autoLogin = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
      ),
      backgroundColor: const Color(0xFFF5F5F5),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 20),
            
            // 通知设置
            ProfileWidgets.buildSectionTitle('通知设置'),
            ProfileWidgets.buildMenuTile(
              icon: Icons.notifications,
              title: '推送通知',
              showArrow: false,
              trailing: Switch(
                value: _notificationsEnabled,
                onChanged: (value) {
                  setState(() {
                    _notificationsEnabled = value;
                  });
                  _showToast('推送通知已${value ? '开启' : '关闭'}');
                },
                activeColor: const Color(0xFF4A90E2),
              ),
              onTap: () {},
            ),
            
            // 隐私设置
            ProfileWidgets.buildSectionTitle('隐私设置'),
            ProfileWidgets.buildMenuTile(
              icon: Icons.location_on,
              title: '位置服务',
              showArrow: false,
              trailing: Switch(
                value: _locationEnabled,
                onChanged: (value) {
                  setState(() {
                    _locationEnabled = value;
                  });
                  _showToast('位置服务已${value ? '开启' : '关闭'}');
                },
                activeColor: const Color(0xFF4A90E2),
              ),
              onTap: () {},
            ),
            ProfileWidgets.buildMenuTile(
              icon: Icons.privacy_tip,
              title: '隐私政策',
              onTap: () {
                _showToast('隐私政策页面待实现');
              },
            ),
            
            // 账户设置
            ProfileWidgets.buildSectionTitle('账户设置'),
            ProfileWidgets.buildMenuTile(
              icon: Icons.login,
              title: '自动登录',
              showArrow: false,
              trailing: Switch(
                value: _autoLogin,
                onChanged: (value) {
                  setState(() {
                    _autoLogin = value;
                  });
                  _showToast('自动登录已${value ? '开启' : '关闭'}');
                },
                activeColor: const Color(0xFF4A90E2),
              ),
              onTap: () {},
            ),
            ProfileWidgets.buildMenuTile(
              icon: Icons.edit,
              title: '修改密码',
              onTap: () {
                _showToast('修改密码功能待实现');
              },
            ),
            ProfileWidgets.buildMenuTile(
              icon: Icons.person,
              title: '账户信息',
              onTap: () {
                _showToast('账户信息页面待实现');
              },
            ),
            
            // 应用设置
            ProfileWidgets.buildSectionTitle('应用设置'),
            ProfileWidgets.buildMenuTile(
              icon: Icons.language,
              title: '语言设置',
              onTap: () {
                _showLanguageDialog();
              },
            ),
            ProfileWidgets.buildMenuTile(
              icon: Icons.palette,
              title: '主题设置',
              onTap: () {
                _showThemeDialog();
              },
            ),
            ProfileWidgets.buildMenuTile(
              icon: Icons.storage,
              title: '清除缓存',
              onTap: () {
                _showClearCacheDialog();
              },
            ),
            
            // 其他
            ProfileWidgets.buildSectionTitle('其他'),
            ProfileWidgets.buildMenuTile(
              icon: Icons.info,
              title: '关于应用',
              onTap: () {
                _showAboutDialog();
              },
            ),
            ProfileWidgets.buildMenuTile(
              icon: Icons.feedback,
              title: '意见反馈',
              onTap: () {
                _showToast('意见反馈功能待实现');
              },
            ),
            ProfileWidgets.buildMenuTile(
              icon: Icons.star_rate,
              title: '给应用评分',
              onTap: () {
                _showToast('应用评分功能待实现');
              },
            ),
            
            const SizedBox(height: 30),
          ],
        ),
      ),
    );
  }

  void _showToast(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择语言'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('简体中文'),
              leading: const Icon(Icons.check, color: Color(0xFF4A90E2)),
              onTap: () {
                Navigator.pop(context);
                _showToast('语言已设置为简体中文');
              },
            ),
            ListTile(
              title: const Text('English'),
              onTap: () {
                Navigator.pop(context);
                _showToast('Language set to English');
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showThemeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择主题'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('浅色主题'),
              leading: const Icon(Icons.check, color: Color(0xFF4A90E2)),
              onTap: () {
                Navigator.pop(context);
                _showToast('已切换到浅色主题');
              },
            ),
            ListTile(
              title: const Text('深色主题'),
              onTap: () {
                Navigator.pop(context);
                _showToast('深色主题功能待实现');
              },
            ),
            ListTile(
              title: const Text('跟随系统'),
              onTap: () {
                Navigator.pop(context);
                _showToast('跟随系统主题功能待实现');
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除缓存'),
        content: const Text('确定要清除应用缓存吗？这将删除临时文件和离线数据。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _showToast('缓存清除功能待实现');
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('关于钓鱼了么'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('版本：1.0.0'),
            SizedBox(height: 8),
            Text('钓鱼了么是一款专为钓鱼爱好者打造的社交分享平台。'),
            SizedBox(height: 8),
            Text('在这里，您可以：'),
            Text('• 发现和分享钓点'),
            Text('• 记录钓鱼心得'),
            Text('• 与钓友交流经验'),
            Text('• 收藏喜欢的内容'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}

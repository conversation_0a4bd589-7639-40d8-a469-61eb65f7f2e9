import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/message/conversation.dart';
import '../../services/service_locator.dart';
import 'conversation_detail_page.dart';
import 'notification_page.dart';

/// 消息列表页面
class MessageListPage extends StatefulWidget {
  const MessageListPage({super.key});

  @override
  State<MessageListPage> createState() => _MessageListPageState();
}

class _MessageListPageState extends State<MessageListPage> 
    with AutomaticKeepAliveClientMixin {
  List<Conversation> _conversations = [];
  bool _isLoading = true;
  int _systemNotificationCount = 0;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadConversations();
  }

  /// 加载对话列表
  Future<void> _loadConversations() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: 从服务获取真实数据
      await Future.delayed(const Duration(milliseconds: 500)); // 模拟网络请求
      
      // 模拟数据
      final conversations = [
        Conversation(
          id: '1',
          otherUserId: 'user1',
          otherUserName: '张三',
          otherUserAvatar: 'https://via.placeholder.com/50',
          lastMessage: '你好，这个钓点怎么样？',
          lastMessageTime: DateTime.now().subtract(const Duration(minutes: 30)),
          unreadCount: 2,
        ),
        Conversation(
          id: '2',
          otherUserId: 'user2',
          otherUserName: '李四',
          lastMessage: '[图片]',
          lastMessageTime: DateTime.now().subtract(const Duration(hours: 1)),
          lastMessageType: 'image',
          unreadCount: 0,
        ),
        Conversation(
          id: '3',
          otherUserId: 'user3',
          otherUserName: '王五',
          lastMessage: '你好，交个朋友...',
          lastMessageTime: DateTime.now().subtract(const Duration(hours: 2)),
          unreadCount: 1,
          isPending: true,
        ),
      ];

      if (mounted) {
        setState(() {
          _conversations = conversations;
          _systemNotificationCount = 3; // 模拟系统通知数量
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载失败: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadConversations,
      child: Column(
        children: [
          // 系统通知入口
          _buildSystemNotificationEntry(),
          
          // 对话列表
          Expanded(
            child: _conversations.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    itemCount: _conversations.length,
                    itemBuilder: (context, index) {
                      return _buildConversationItem(_conversations[index]);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  /// 系统通知入口
  Widget _buildSystemNotificationEntry() {
    return Container(
      color: Colors.white,
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.orange.shade400,
                Colors.red.shade400,
              ],
            ),
            borderRadius: BorderRadius.circular(25),
          ),
          child: const Icon(
            Icons.notifications,
            color: Colors.white,
            size: 24,
          ),
        ),
        title: const Text(
          '系统通知',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          _systemNotificationCount > 0 
              ? '您有 $_systemNotificationCount 条新通知'
              : '暂无新通知',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_systemNotificationCount > 0)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _systemNotificationCount > 99 ? '99+' : _systemNotificationCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const NotificationPage(),
            ),
          );
        },
      ),
    );
  }

  /// 对话列表项
  Widget _buildConversationItem(Conversation conversation) {
    return Container(
      color: Colors.white,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Stack(
          children: [
            CircleAvatar(
              radius: 25,
              backgroundColor: Colors.grey[300],
              backgroundImage: conversation.otherUserAvatar != null
                  ? NetworkImage(conversation.otherUserAvatar!)
                  : null,
              child: conversation.otherUserAvatar == null
                  ? Text(
                      conversation.otherUserName.isNotEmpty
                          ? conversation.otherUserName[0]
                          : '?',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    )
                  : null,
            ),
            // 在线状态指示器（可选）
            if (!conversation.isPending)
              Positioned(
                right: 0,
                bottom: 0,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    border: Border.all(color: Colors.white, width: 2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                conversation.otherUserName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (conversation.isPending)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Text(
                  '陌生人',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              conversation.displayLastMessage,
              style: TextStyle(
                fontSize: 14,
                color: conversation.unreadCount > 0 
                    ? Colors.black87 
                    : Colors.grey[600],
                fontWeight: conversation.unreadCount > 0 
                    ? FontWeight.w500 
                    : FontWeight.normal,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            if (conversation.isPending) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  _buildActionButton(
                    '接受',
                    Colors.green,
                    () => _acceptConversation(conversation),
                  ),
                  const SizedBox(width: 12),
                  _buildActionButton(
                    '拒绝',
                    Colors.red,
                    () => _rejectConversation(conversation),
                  ),
                ],
              ),
            ],
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              conversation.displayTime,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: 4),
            if (conversation.unreadCount > 0 && !conversation.isPending)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10),
                ),
                constraints: const BoxConstraints(
                  minWidth: 20,
                  minHeight: 20,
                ),
                child: Text(
                  conversation.unreadCount > 99 ? '99+' : conversation.unreadCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
        onTap: conversation.isPending ? null : () => _openConversation(conversation),
      ),
    );
  }

  /// 操作按钮
  Widget _buildActionButton(String text, Color color, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(15),
        ),
        child: Text(
          text,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  /// 空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FaIcon(
            FontAwesomeIcons.message,
            size: 64,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Text(
            '暂无消息',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '开始与钓友们交流吧',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }

  /// 打开对话
  void _openConversation(Conversation conversation) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ConversationDetailPage(
          conversation: conversation,
        ),
      ),
    ).then((_) {
      // 返回时刷新列表
      _loadConversations();
    });
  }

  /// 接受陌生人对话
  void _acceptConversation(Conversation conversation) async {
    try {
      // TODO: 调用服务接受对话
      await Future.delayed(const Duration(milliseconds: 300));
      
      // 更新对话状态
      setState(() {
        final index = _conversations.indexWhere((c) => c.id == conversation.id);
        if (index != -1) {
          _conversations[index] = Conversation(
            id: conversation.id,
            otherUserId: conversation.otherUserId,
            otherUserName: conversation.otherUserName,
            otherUserAvatar: conversation.otherUserAvatar,
            lastMessage: conversation.lastMessage,
            lastMessageTime: conversation.lastMessageTime,
            lastMessageType: conversation.lastMessageType,
            unreadCount: conversation.unreadCount,
            isPending: false, // 不再是待验证状态
          );
        }
      });
      
      // 跳转到对话页面
      _openConversation(_conversations.firstWhere((c) => c.id == conversation.id));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('操作失败: $e')),
      );
    }
  }

  /// 拒绝陌生人对话
  void _rejectConversation(Conversation conversation) async {
    try {
      // TODO: 调用服务拒绝对话并拉黑用户
      await Future.delayed(const Duration(milliseconds: 300));
      
      // 从列表中移除
      setState(() {
        _conversations.removeWhere((c) => c.id == conversation.id);
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('已拒绝并拉黑该用户')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('操作失败: $e')),
      );
    }
  }
}
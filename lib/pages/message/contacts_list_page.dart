import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/message/contact_category.dart';
import '../../services/service_locator.dart';
import 'contacts_detail_page.dart';

/// 联系人列表页面
class ContactsListPage extends StatefulWidget {
  const ContactsListPage({super.key});

  @override
  State<ContactsListPage> createState() => _ContactsListPageState();
}

class _ContactsListPageState extends State<ContactsListPage> 
    with AutomaticKeepAliveClientMixin {
  List<ContactCategory> _categories = [];
  bool _isLoading = true;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadContactCategories();
  }

  /// 加载联系人分类数据
  Future<void> _loadContactCategories() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: 从服务获取真实数据
      await Future.delayed(const Duration(milliseconds: 500)); // 模拟网络请求
      
      // 模拟数据
      final categories = [
        ContactCategory(
          name: '好友',
          icon: '👥',
          count: 23,
          type: ContactCategoryType.friends,
        ),
        ContactCategory(
          name: '我关注的',
          icon: '👁️',
          count: 156,
          type: ContactCategoryType.following,
        ),
        ContactCategory(
          name: '关注我的',
          icon: '👤',
          count: 89,
          type: ContactCategoryType.followers,
          showBadge: true,
          badgeCount: 5, // 新关注者
        ),
        ContactCategory(
          name: '黑名单',
          icon: '🚫',
          count: 2,
          type: ContactCategoryType.blocked,
        ),
      ];

      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载失败: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadContactCategories,
      child: _categories.isEmpty
          ? _buildEmptyState()
          : ListView.separated(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: _categories.length,
              separatorBuilder: (context, index) => const Divider(
                height: 1,
                indent: 72,
                endIndent: 16,
              ),
              itemBuilder: (context, index) {
                return _buildCategoryItem(_categories[index]);
              },
            ),
    );
  }

  /// 联系人分类项
  Widget _buildCategoryItem(ContactCategory category) {
    return Container(
      color: Colors.white,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: _getCategoryColor(category.type),
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: _getCategoryColor(category.type).withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: _getCategoryIcon(category.type),
          ),
        ),
        title: Row(
          children: [
            Text(
              category.name,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 8),
            if (category.count > 0)
              Text(
                category.displayCount,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.normal,
                ),
              ),
          ],
        ),
        subtitle: Text(
          _getCategoryDescription(category.type),
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey[500],
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (category.showBadge && category.badgeCount > 0)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  category.badgeCount > 99 ? '99+' : category.badgeCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
        onTap: () => _openCategoryDetail(category),
      ),
    );
  }

  /// 获取分类颜色
  Color _getCategoryColor(ContactCategoryType type) {
    switch (type) {
      case ContactCategoryType.friends:
        return Colors.blue;
      case ContactCategoryType.following:
        return Colors.green;
      case ContactCategoryType.followers:
        return Colors.orange;
      case ContactCategoryType.blocked:
        return Colors.red;
    }
  }

  /// 获取分类图标
  Widget _getCategoryIcon(ContactCategoryType type) {
    switch (type) {
      case ContactCategoryType.friends:
        return const FaIcon(
          FontAwesomeIcons.userGroup,
          color: Colors.white,
          size: 20,
        );
      case ContactCategoryType.following:
        return const FaIcon(
          FontAwesomeIcons.eye,
          color: Colors.white,
          size: 20,
        );
      case ContactCategoryType.followers:
        return const FaIcon(
          FontAwesomeIcons.user,
          color: Colors.white,
          size: 20,
        );
      case ContactCategoryType.blocked:
        return const FaIcon(
          FontAwesomeIcons.ban,
          color: Colors.white,
          size: 20,
        );
    }
  }

  /// 获取分类描述
  String _getCategoryDescription(ContactCategoryType type) {
    switch (type) {
      case ContactCategoryType.friends:
        return '互相关注的钓友';
      case ContactCategoryType.following:
        return '我关注的用户';
      case ContactCategoryType.followers:
        return '关注我的用户';
      case ContactCategoryType.blocked:
        return '已拉黑的用户';
    }
  }

  /// 空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FaIcon(
            FontAwesomeIcons.addressBook,
            size: 64,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Text(
            '暂无联系人',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '去发现更多钓友吧',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }

  /// 打开分类详情
  void _openCategoryDetail(ContactCategory category) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ContactsDetailPage(
          category: category,
        ),
      ),
    ).then((_) {
      // 返回时刷新数据
      _loadContactCategories();
    });
  }
}
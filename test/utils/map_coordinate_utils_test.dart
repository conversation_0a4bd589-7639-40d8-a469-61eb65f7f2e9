import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:latlong2/latlong.dart';

import 'package:fishing_app/utils/map_coordinate_utils.dart';

void main() {
  group('MapCoordinateUtils Tests', () {
    late MapCamera camera;
    late Size screenSize;

    setUp(() {
      // 创建测试用的地图相机
      camera = MapCamera(
        crs: const Epsg3857(),
        center: const LatLng(39.9042, 116.4074), // 北京
        zoom: 15.0,
        rotation: 0.0,
        nonRotatedSize: const Size(400, 600),
      );

      screenSize = const Size(400, 600);
    });

    test(
      'calculateLocationAtScreenPosition should return correct coordinates',
      () {
        // 测试屏幕中心位置
        final centerLocation =
            MapCoordinateUtils.calculateLocationAtScreenPosition(
              camera,
              screenHeightPercent: 0.5,
              screenWidthPercent: 0.5,
              screenSize: screenSize,
            );

        // 屏幕中心应该对应地图中心
        expect(centerLocation.latitude, closeTo(camera.center.latitude, 0.001));
        expect(
          centerLocation.longitude,
          closeTo(camera.center.longitude, 0.001),
        );
      },
    );

    test(
      'calculateLocationAt25PercentHeight should return correct coordinates',
      () {
        final location = MapCoordinateUtils.calculateLocationAt25PercentHeight(
          camera,
          screenSize,
        );

        // 25%高度处的坐标应该在地图中心的北方
        expect(location.latitude, greaterThan(camera.center.latitude));
        expect(location.longitude, closeTo(camera.center.longitude, 0.001));
      },
    );

    test('coordinate offset calculation should be correct', () {
      // 测试坐标偏移计算逻辑

      // 模拟目标坐标当前在屏幕中心
      final currentScreenOffset = Offset(
        screenSize.width / 2,
        screenSize.height / 2,
      );

      // 目标位置是25%高度、50%宽度
      final targetScreenOffset = Offset(
        screenSize.width * 0.5,
        screenSize.height * 0.25,
      );

      // 计算需要移动的偏移（地图移动方向与坐标移动方向相反）
      final screenOffsetDelta = currentScreenOffset - targetScreenOffset;

      // 验证偏移方向：要让坐标从中心(50%高度)移动到25%高度，地图需要向下移动
      expect(screenOffsetDelta.dx, equals(0.0)); // 水平方向不变
      expect(screenOffsetDelta.dy, greaterThan(0.0)); // 垂直方向向下（正值）

      // 具体数值验证
      expect(
        screenOffsetDelta.dy,
        equals(screenSize.height * 0.25),
      ); // 25%的屏幕高度
    });

    test('percentToPixel should convert correctly', () {
      final pixelOffset = MapCoordinateUtils.percentToPixel(
        0.5,
        0.25,
        screenSize,
      );

      expect(pixelOffset.dx, equals(200.0)); // 50% of 400
      expect(pixelOffset.dy, equals(150.0)); // 25% of 600
    });

    test('pixelToPercent should convert correctly', () {
      const pixelOffset = Offset(200.0, 150.0);
      final percentOffset = MapCoordinateUtils.pixelToPercent(
        pixelOffset,
        screenSize,
      );

      expect(percentOffset.dx, equals(0.5)); // 200/400
      expect(percentOffset.dy, equals(0.25)); // 150/600
    });

    test('isScreenPositionValid should validate correctly', () {
      // 有效位置
      expect(
        MapCoordinateUtils.isScreenPositionValid(
          const Offset(200, 300),
          screenSize,
        ),
        isTrue,
      );

      // 无效位置（超出边界）
      expect(
        MapCoordinateUtils.isScreenPositionValid(
          const Offset(500, 300),
          screenSize,
        ),
        isFalse,
      );

      expect(
        MapCoordinateUtils.isScreenPositionValid(
          const Offset(200, 700),
          screenSize,
        ),
        isFalse,
      );

      // 边界位置
      expect(
        MapCoordinateUtils.isScreenPositionValid(
          const Offset(0, 0),
          screenSize,
        ),
        isTrue,
      );

      expect(
        MapCoordinateUtils.isScreenPositionValid(
          const Offset(400, 600),
          screenSize,
        ),
        isTrue,
      );
    });

    test('coordinate conversion should be reversible', () {
      const testCoordinate = LatLng(39.9042, 116.4074);

      // 将地理坐标转换为屏幕坐标
      final screenOffset = camera.latLngToScreenOffset(testCoordinate);

      // 再转换回地理坐标
      final convertedCoordinate = camera.screenOffsetToLatLng(screenOffset);

      // 应该非常接近原始坐标
      expect(
        convertedCoordinate.latitude,
        closeTo(testCoordinate.latitude, 1e-6),
      );
      expect(
        convertedCoordinate.longitude,
        closeTo(testCoordinate.longitude, 1e-6),
      );
    });

    test('screen position calculations should be consistent', () {
      // 测试多个屏幕位置的一致性
      final positions = [
        {'height': 0.0, 'width': 0.0}, // 左上角
        {'height': 0.0, 'width': 1.0}, // 右上角
        {'height': 1.0, 'width': 0.0}, // 左下角
        {'height': 1.0, 'width': 1.0}, // 右下角
        {'height': 0.5, 'width': 0.5}, // 中心
        {'height': 0.25, 'width': 0.5}, // 25%高度中心
      ];

      for (final pos in positions) {
        final heightPercent = pos['height']!;
        final widthPercent = pos['width']!;

        // 计算该位置的地理坐标
        final coordinate = MapCoordinateUtils.calculateLocationAtScreenPosition(
          camera,
          screenHeightPercent: heightPercent,
          screenWidthPercent: widthPercent,
          screenSize: screenSize,
        );

        // 将地理坐标转换回屏幕坐标
        final screenOffset = camera.latLngToScreenOffset(coordinate);

        // 计算期望的屏幕坐标
        final expectedOffset = MapCoordinateUtils.percentToPixel(
          widthPercent,
          heightPercent,
          screenSize,
        );

        // 验证一致性（允许小误差）
        expect(screenOffset.dx, closeTo(expectedOffset.dx, 1.0));
        expect(screenOffset.dy, closeTo(expectedOffset.dy, 1.0));
      }
    });
  });
}

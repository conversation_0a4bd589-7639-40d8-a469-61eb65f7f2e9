#!/bin/bash

# 认证测试脚本
# 用于验证PocketBase认证是否正常工作

echo "🔐 开始测试PocketBase认证..."

# 配置
POCKETBASE_URL="http://117.72.60.131:8090"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试1: 检查PocketBase服务
echo -e "\n${YELLOW}测试1: 检查PocketBase服务${NC}"
if curl -s "$POCKETBASE_URL/api/health" > /dev/null; then
    echo -e "${GREEN}✅ PocketBase服务正常${NC}"
else
    echo -e "${RED}❌ PocketBase服务不可用${NC}"
    exit 1
fi

# 测试2: 测试无认证的API
echo -e "\n${YELLOW}测试2: 测试无认证的API${NC}"
BASIC_TEST=$(curl -s "$POCKETBASE_URL/api/test")
if echo "$BASIC_TEST" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 基础API测试通过${NC}"
else
    echo -e "${RED}❌ 基础API测试失败${NC}"
    echo "响应: $BASIC_TEST"
fi

# 测试3: 测试需要认证的API（应该返回401）
echo -e "\n${YELLOW}测试3: 测试需要认证的API（无token）${NC}"
AUTH_TEST_NO_TOKEN=$(curl -s "$POCKETBASE_URL/api/test/auth")
if echo "$AUTH_TEST_NO_TOKEN" | grep -q '"authenticated":false'; then
    echo -e "${GREEN}✅ 无token认证测试正常（返回未认证状态）${NC}"
else
    echo -e "${YELLOW}⚠️  无token认证测试响应异常${NC}"
    echo "响应: $AUTH_TEST_NO_TOKEN"
fi

# 测试4: 创建测试用户
echo -e "\n${YELLOW}测试4: 创建测试用户${NC}"
TEST_EMAIL="test_$(date +%s)@example.com"
TEST_PASSWORD="test123456"

CREATE_USER_RESULT=$(curl -s -X POST "$POCKETBASE_URL/api/test/create-test-user" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\"}")

if echo "$CREATE_USER_RESULT" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 测试用户创建成功${NC}"
    USER_ID=$(echo "$CREATE_USER_RESULT" | grep -o '"userId":"[^"]*' | cut -d'"' -f4)
    echo "用户ID: $USER_ID"
else
    echo -e "${YELLOW}⚠️  测试用户创建失败（可能已存在）${NC}"
    echo "响应: $CREATE_USER_RESULT"
fi

# 测试5: 用户登录获取token
echo -e "\n${YELLOW}测试5: 用户登录获取token${NC}"
LOGIN_RESULT=$(curl -s -X POST "$POCKETBASE_URL/api/test/login" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\"}")

if echo "$LOGIN_RESULT" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 用户登录成功${NC}"
    TOKEN=$(echo "$LOGIN_RESULT" | grep -o '"token":"[^"]*' | cut -d'"' -f4)
    echo "Token: ${TOKEN:0:20}..."
else
    echo -e "${RED}❌ 用户登录失败${NC}"
    echo "响应: $LOGIN_RESULT"
    exit 1
fi

# 测试6: 使用token测试认证
echo -e "\n${YELLOW}测试6: 使用token测试认证${NC}"
AUTH_TEST_WITH_TOKEN=$(curl -s "$POCKETBASE_URL/api/test/auth" \
  -H "Authorization: Bearer $TOKEN")

if echo "$AUTH_TEST_WITH_TOKEN" | grep -q '"authenticated":true'; then
    echo -e "${GREEN}✅ 带token认证测试成功${NC}"
    USER_EMAIL=$(echo "$AUTH_TEST_WITH_TOKEN" | grep -o '"userEmail":"[^"]*' | cut -d'"' -f4)
    echo "认证用户: $USER_EMAIL"
else
    echo -e "${RED}❌ 带token认证测试失败${NC}"
    echo "响应: $AUTH_TEST_WITH_TOKEN"
fi

# 测试7: 测试预签名URL认证
echo -e "\n${YELLOW}测试7: 测试预签名URL认证${NC}"
PRESIGNED_AUTH_TEST=$(curl -s -X POST "$POCKETBASE_URL/api/test/generate-upload-url-auth" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

if echo "$PRESIGNED_AUTH_TEST" | grep -q '"authenticated":true'; then
    echo -e "${GREEN}✅ 预签名URL认证测试成功${NC}"
else
    echo -e "${RED}❌ 预签名URL认证测试失败${NC}"
    echo "响应: $PRESIGNED_AUTH_TEST"
fi

# 测试8: 创建测试钓点
echo -e "\n${YELLOW}测试8: 创建测试钓点${NC}"
CREATE_SPOT_RESULT=$(curl -s -X POST "$POCKETBASE_URL/api/test/create-test-spot" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

if echo "$CREATE_SPOT_RESULT" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 测试钓点创建成功${NC}"
    SPOT_ID=$(echo "$CREATE_SPOT_RESULT" | grep -o '"spotId":"[^"]*' | cut -d'"' -f4)
    echo "钓点ID: $SPOT_ID"
    
    # 测试9: 测试完整的预签名URL生成
    echo -e "\n${YELLOW}测试9: 测试完整的预签名URL生成${NC}"
    PRESIGNED_URL_RESULT=$(curl -s -X POST "$POCKETBASE_URL/api/generate-upload-url" \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d "{\"spotId\":\"$SPOT_ID\",\"fileName\":\"test.jpg\",\"fileType\":\"image/jpeg\"}")
    
    if echo "$PRESIGNED_URL_RESULT" | grep -q '"uploadUrl"'; then
        echo -e "${GREEN}✅ 预签名URL生成成功${NC}"
        echo "预签名URL已生成"
    else
        echo -e "${RED}❌ 预签名URL生成失败${NC}"
        echo "响应: $PRESIGNED_URL_RESULT"
    fi
else
    echo -e "${RED}❌ 测试钓点创建失败${NC}"
    echo "响应: $CREATE_SPOT_RESULT"
fi

echo -e "\n${GREEN}🎉 认证测试完成！${NC}"
echo "如果所有测试都通过，说明认证系统工作正常。"

#!/bin/bash

# 简单测试脚本
# 用于验证PocketBase基本功能

echo "🧪 开始简单测试..."

# 配置
POCKETBASE_URL="http://117.72.60.131:8090"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试1: 基本GET请求
echo -e "\n${YELLOW}测试1: 基本GET请求${NC}"
SIMPLE_GET=$(curl -s "$POCKETBASE_URL/api/simple/test")
if echo "$SIMPLE_GET" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 基本GET测试通过${NC}"
    echo "响应: $SIMPLE_GET"
else
    echo -e "${RED}❌ 基本GET测试失败${NC}"
    echo "响应: $SIMPLE_GET"
fi

# 测试2: 基本POST请求（无认证）
echo -e "\n${YELLOW}测试2: 基本POST请求（无认证）${NC}"
SIMPLE_POST=$(curl -s -X POST "$POCKETBASE_URL/api/simple/echo" \
  -H "Content-Type: application/json" \
  -d '{"test":"hello","number":123}')

if echo "$SIMPLE_POST" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 基本POST测试通过${NC}"
    echo "响应: $SIMPLE_POST"
else
    echo -e "${RED}❌ 基本POST测试失败${NC}"
    echo "响应: $SIMPLE_POST"
fi

# 测试3: 需要认证的POST请求（无token，应该返回401）
echo -e "\n${YELLOW}测试3: 需要认证的POST请求（无token）${NC}"
AUTH_POST_NO_TOKEN=$(curl -s -X POST "$POCKETBASE_URL/api/simple/auth-echo" \
  -H "Content-Type: application/json" \
  -d '{"test":"hello"}')

if echo "$AUTH_POST_NO_TOKEN" | grep -q '"error":"需要认证"'; then
    echo -e "${GREEN}✅ 无token认证测试正常（返回401）${NC}"
else
    echo -e "${YELLOW}⚠️  无token认证测试响应异常${NC}"
    echo "响应: $AUTH_POST_NO_TOKEN"
fi

# 测试4: 尝试使用PocketBase内置API创建用户
echo -e "\n${YELLOW}测试4: 使用PocketBase内置API创建用户${NC}"
TEST_EMAIL="test_$(date +%s)@example.com"
TEST_PASSWORD="test123456"

# 先检查用户集合的结构
echo "检查用户集合结构..."
USER_COLLECTION=$(curl -s "$POCKETBASE_URL/api/collections/users")
echo "用户集合信息: $USER_COLLECTION"

CREATE_USER_BUILTIN=$(curl -s -X POST "$POCKETBASE_URL/api/collections/users/records" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\",\"passwordConfirm\":\"$TEST_PASSWORD\"}")

if echo "$CREATE_USER_BUILTIN" | grep -q '"id"'; then
    echo -e "${GREEN}✅ 内置API创建用户成功${NC}"
    USER_ID=$(echo "$CREATE_USER_BUILTIN" | grep -o '"id":"[^"]*' | cut -d'"' -f4)
    echo "用户ID: $USER_ID"
    
    # 测试5: 使用内置API登录
    echo -e "\n${YELLOW}测试5: 使用内置API登录${NC}"
    LOGIN_BUILTIN=$(curl -s -X POST "$POCKETBASE_URL/api/collections/users/auth-with-password" \
      -H "Content-Type: application/json" \
      -d "{\"identity\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\"}")
    
    if echo "$LOGIN_BUILTIN" | grep -q '"token"'; then
        echo -e "${GREEN}✅ 内置API登录成功${NC}"
        TOKEN=$(echo "$LOGIN_BUILTIN" | grep -o '"token":"[^"]*' | cut -d'"' -f4)
        echo "Token: ${TOKEN:0:20}..."
        
        # 测试6: 使用token测试认证POST
        echo -e "\n${YELLOW}测试6: 使用token测试认证POST${NC}"
        AUTH_POST_WITH_TOKEN=$(curl -s -X POST "$POCKETBASE_URL/api/simple/auth-echo" \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer $TOKEN" \
          -d '{"test":"authenticated hello","number":456}')
        
        if echo "$AUTH_POST_WITH_TOKEN" | grep -q '"success":true'; then
            echo -e "${GREEN}✅ 带token认证POST测试成功${NC}"
            echo "响应: $AUTH_POST_WITH_TOKEN"
        else
            echo -e "${RED}❌ 带token认证POST测试失败${NC}"
            echo "响应: $AUTH_POST_WITH_TOKEN"
        fi
        
    else
        echo -e "${RED}❌ 内置API登录失败${NC}"
        echo "响应: $LOGIN_BUILTIN"
    fi
    
else
    echo -e "${YELLOW}⚠️  内置API创建用户失败（可能已存在）${NC}"
    echo "响应: $CREATE_USER_BUILTIN"
fi

echo -e "\n${GREEN}🎉 简单测试完成！${NC}"
echo "如果基本功能都正常，我们可以继续调试复杂的功能。"

#!/bin/bash

# 集合字段验证测试脚本

SERVER_URL="http://117.72.60.131:8090"

echo "=== PocketBase 集合字段验证测试 ==="
echo "服务器地址: $SERVER_URL"
echo ""

# 1. 验证所有集合的详细信息
echo "1. 验证所有集合的详细字段信息..."
echo "curl -X GET $SERVER_URL/api/verify-collections"
echo ""
curl -X GET "$SERVER_URL/api/verify-collections" | jq '.' 2>/dev/null || curl -X GET "$SERVER_URL/api/verify-collections"
echo ""
echo "========================================"

# 2. 专门验证 spot_likes 集合
echo "2. 专门验证 spot_likes 集合..."
echo "curl -X GET $SERVER_URL/api/verify-spot-likes"
echo ""
curl -X GET "$SERVER_URL/api/verify-spot-likes" | jq '.' 2>/dev/null || curl -X GET "$SERVER_URL/api/verify-spot-likes"
echo ""
echo "========================================"

# 3. 测试 spot_likes 的查询功能
echo "3. 测试 spot_likes 的查询功能..."
echo "curl -X GET $SERVER_URL/api/test-spot-likes-query"
echo ""
curl -X GET "$SERVER_URL/api/test-spot-likes-query" | jq '.' 2>/dev/null || curl -X GET "$SERVER_URL/api/test-spot-likes-query"
echo ""

echo "=== 验证测试完成 ==="
echo ""
echo "这些测试将帮助我们了解："
echo "1. 所有集合的实际字段定义"
echo "2. spot_likes 集合的具体配置"
echo "3. 权限设置和数据访问情况"
echo "4. 字段名称是否与代码匹配"
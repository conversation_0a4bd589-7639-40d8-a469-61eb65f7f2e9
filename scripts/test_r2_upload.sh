#!/bin/bash

# R2上传功能测试脚本
# 用于验证AWS S3 Signature V4实现是否正确

echo "🚀 开始测试R2上传功能..."

# 配置
POCKETBASE_URL="http://117.72.60.131:8090"  # 使用实际的服务器地址
TEST_IMAGE_PATH="test_image.jpg"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 创建测试图片（如果不存在）
if [ ! -f "$TEST_IMAGE_PATH" ]; then
    echo "📸 创建测试图片..."
    # 创建一个简单的测试图片（需要ImageMagick）
    if command -v convert &> /dev/null; then
        convert -size 100x100 xc:red "$TEST_IMAGE_PATH"
        echo "✅ 测试图片创建成功"
    else
        echo "❌ 需要安装ImageMagick来创建测试图片"
        echo "或者手动放置一个名为 $TEST_IMAGE_PATH 的图片文件"
        exit 1
    fi
fi

# 测试1: 检查PocketBase服务
echo -e "\n${YELLOW}测试1: 检查PocketBase服务${NC}"
if curl -s "$POCKETBASE_URL/api/health" > /dev/null; then
    echo -e "${GREEN}✅ PocketBase服务正常${NC}"
else
    echo -e "${RED}❌ PocketBase服务不可用${NC}"
    exit 1
fi

# 测试2: 测试签名算法
echo -e "\n${YELLOW}测试2: 测试签名算法${NC}"
SIGNATURE_TEST=$(curl -s "$POCKETBASE_URL/api/test/signature")
if echo "$SIGNATURE_TEST" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 签名算法测试通过${NC}"
    echo "预签名URL: $(echo "$SIGNATURE_TEST" | grep -o '"presignedUrl":"[^"]*' | cut -d'"' -f4)"
else
    echo -e "${RED}❌ 签名算法测试失败${NC}"
    echo "错误信息: $SIGNATURE_TEST"
fi

# 测试3: 测试签名组件
echo -e "\n${YELLOW}测试3: 测试签名组件${NC}"
COMPONENTS_TEST=$(curl -s "$POCKETBASE_URL/api/test/signature/components")
if echo "$COMPONENTS_TEST" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 签名组件测试通过${NC}"
else
    echo -e "${RED}❌ 签名组件测试失败${NC}"
    echo "错误信息: $COMPONENTS_TEST"
fi

# 测试4: 模拟获取预签名URL（需要认证）
echo -e "\n${YELLOW}测试4: 模拟预签名URL获取${NC}"
echo "注意: 此测试需要有效的用户认证token"

# 创建测试用户（如果需要）
echo "如需完整测试，请："
echo "1. 在Flutter应用中登录用户"
echo "2. 创建一个钓点"
echo "3. 尝试上传图片"

# 测试5: 检查R2配置
echo -e "\n${YELLOW}测试5: 检查R2配置${NC}"
echo "请确认以下配置正确："
echo "- R2 Endpoint: 已配置"
echo "- R2 Bucket Name: 已配置"
echo "- R2 Access Key ID: 已配置"
echo "- R2 Secret Access Key: 已配置"
echo -e "${YELLOW}⚠️  注意: 生产环境应使用环境变量存储敏感信息${NC}"

# 清理
if [ -f "$TEST_IMAGE_PATH" ] && command -v convert &> /dev/null; then
    rm "$TEST_IMAGE_PATH"
    echo "🧹 清理测试文件"
fi

echo -e "\n${GREEN}🎉 测试完成！${NC}"
echo "如果所有测试都通过，您的R2上传功能应该可以正常工作。"
echo "如有问题，请检查："
echo "1. PocketBase服务是否正常运行"
echo "2. R2配置是否正确"
echo "3. 网络连接是否正常"
echo "4. 用户认证是否有效"

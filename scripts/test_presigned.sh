#!/bin/bash

# 预签名URL测试脚本
# 用于测试我们修复后的预签名URL生成功能

echo "🔗 开始测试预签名URL功能..."

# 配置
POCKETBASE_URL="http://117.72.60.131:8090"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试1: 测试签名算法（无需认证）
echo -e "\n${YELLOW}测试1: 测试签名算法${NC}"
SIGNATURE_TEST=$(curl -s "$POCKETBASE_URL/api/test/signature")
if echo "$SIGNATURE_TEST" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 签名算法测试通过${NC}"
    echo "预签名URL已生成"
else
    echo -e "${RED}❌ 签名算法测试失败${NC}"
    echo "响应: $SIGNATURE_TEST"
fi

# 测试2: 测试签名组件
echo -e "\n${YELLOW}测试2: 测试签名组件${NC}"
COMPONENTS_TEST=$(curl -s "$POCKETBASE_URL/api/test/signature/components")
if echo "$COMPONENTS_TEST" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 签名组件测试通过${NC}"
else
    echo -e "${RED}❌ 签名组件测试失败${NC}"
    echo "响应: $COMPONENTS_TEST"
fi

# 测试3: 测试预签名URL认证（无token，应该返回401）
echo -e "\n${YELLOW}测试3: 测试预签名URL认证（无token）${NC}"
PRESIGNED_NO_TOKEN=$(curl -s -X POST "$POCKETBASE_URL/api/test/generate-upload-url-auth")
if echo "$PRESIGNED_NO_TOKEN" | grep -q '"authenticated":false'; then
    echo -e "${GREEN}✅ 预签名URL认证测试正常（返回未认证）${NC}"
else
    echo -e "${YELLOW}⚠️  预签名URL认证测试响应异常${NC}"
    echo "响应: $PRESIGNED_NO_TOKEN"
fi

# 测试4: 尝试使用Flutter应用的现有用户token
echo -e "\n${YELLOW}测试4: 提示 - 需要真实用户token进行完整测试${NC}"
echo "要完整测试预签名URL生成，需要："
echo "1. 在Flutter应用中登录用户"
echo "2. 从应用日志中获取JWT token"
echo "3. 使用该token测试预签名URL生成"
echo ""
echo "示例命令："
echo "curl -X POST \"$POCKETBASE_URL/api/generate-upload-url\" \\"
echo "  -H \"Authorization: Bearer YOUR_JWT_TOKEN\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"spotId\":\"SPOT_ID\",\"fileName\":\"test.jpg\",\"fileType\":\"image/jpeg\"}'"

# 测试5: 测试我们的简单认证端点
echo -e "\n${YELLOW}测试5: 测试简单认证端点（无token）${NC}"
SIMPLE_AUTH_TEST=$(curl -s -X POST "$POCKETBASE_URL/api/simple/auth-echo" \
  -H "Content-Type: application/json" \
  -d '{"test":"auth test"}')

if echo "$SIMPLE_AUTH_TEST" | grep -q '"error":"需要认证"'; then
    echo -e "${GREEN}✅ 简单认证端点正常工作${NC}"
else
    echo -e "${YELLOW}⚠️  简单认证端点响应异常${NC}"
    echo "响应: $SIMPLE_AUTH_TEST"
fi

echo -e "\n${GREEN}🎉 预签名URL基础测试完成！${NC}"
echo ""
echo "总结："
echo "✅ 签名算法正常工作"
echo "✅ PocketBase JavaScript hooks正常"
echo "✅ 认证检查正常"
echo "⚠️  需要真实用户token进行完整测试"
echo ""
echo "下一步："
echo "1. 在Flutter应用中登录用户"
echo "2. 创建钓点"
echo "3. 尝试上传图片"
echo "4. 查看日志中的详细错误信息"

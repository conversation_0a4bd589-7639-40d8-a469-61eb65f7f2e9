#!/bin/bash

# 简化的PocketBase集合查询测试脚本

SERVER_URL="http://117.72.60.131:8090"

echo "=== 简化的 PocketBase 集合查询测试 ==="
echo "服务器地址: $SERVER_URL"
echo ""

# 1. 获取所有已知集合状态
echo "1. 获取所有已知集合状态..."
echo "curl -X GET $SERVER_URL/api/simple-collections"
echo ""
curl -X GET "$SERVER_URL/api/simple-collections" | jq '.' 2>/dev/null || curl -X GET "$SERVER_URL/api/simple-collections"
echo ""
echo "----------------------------------------"

# 2. 检查 spot_likes 集合
echo "2. 检查 spot_likes 集合..."
echo "curl -X GET $SERVER_URL/api/simple-check/spot_likes"
echo ""
curl -X GET "$SERVER_URL/api/simple-check/spot_likes" | jq '.' 2>/dev/null || curl -X GET "$SERVER_URL/api/simple-check/spot_likes"
echo ""
echo "----------------------------------------"

# 3. 检查 fishing_spots 集合
echo "3. 检查 fishing_spots 集合..."
echo "curl -X GET $SERVER_URL/api/simple-check/fishing_spots"
echo ""
curl -X GET "$SERVER_URL/api/simple-check/fishing_spots" | jq '.' 2>/dev/null || curl -X GET "$SERVER_URL/api/simple-check/fishing_spots"
echo ""

echo "=== 测试完成 ==="
echo ""
echo "如果 spot_likes 集合不存在，请运行："
echo "curl -X POST $SERVER_URL/api/create-spot-likes-collection"
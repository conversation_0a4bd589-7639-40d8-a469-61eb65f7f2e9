#!/bin/bash

# PocketBase集合查询API测试脚本
# 使用方法：bash scripts/test_collections_api.sh

SERVER_URL="http://*************:8090"

echo "=== PocketBase 集合查询测试 ==="
echo "服务器地址: $SERVER_URL"
echo ""

# 1. 获取所有集合列表
echo "1. 获取所有集合列表..."
echo "curl -X GET $SERVER_URL/api/list-collections"
echo ""
curl -X GET "$SERVER_URL/api/list-collections" | jq '.' 2>/dev/null || curl -X GET "$SERVER_URL/api/list-collections"
echo ""
echo "----------------------------------------"

# 2. 检查 spot_likes 集合是否存在
echo "2. 检查 spot_likes 集合是否存在..."
echo "curl -X GET $SERVER_URL/api/check-collection/spot_likes"
echo ""
curl -X GET "$SERVER_URL/api/check-collection/spot_likes" | jq '.' 2>/dev/null || curl -X GET "$SERVER_URL/api/check-collection/spot_likes"
echo ""
echo "----------------------------------------"

# 3. 获取 fishing_spots 集合详细信息
echo "3. 获取 fishing_spots 集合详细信息..."
echo "curl -X GET $SERVER_URL/api/collection-info/fishing_spots"
echo ""
curl -X GET "$SERVER_URL/api/collection-info/fishing_spots" | jq '.' 2>/dev/null || curl -X GET "$SERVER_URL/api/collection-info/fishing_spots"
echo ""
echo "----------------------------------------"

# 4. 检查 users 集合
echo "4. 检查 users 集合..."
echo "curl -X GET $SERVER_URL/api/check-collection/users"
echo ""
curl -X GET "$SERVER_URL/api/check-collection/users" | jq '.' 2>/dev/null || curl -X GET "$SERVER_URL/api/check-collection/users"
echo ""
echo "----------------------------------------"

# 5. 获取 spot_photos 集合信息
echo "5. 获取 spot_photos 集合信息..."
echo "curl -X GET $SERVER_URL/api/collection-info/spot_photos"
echo ""
curl -X GET "$SERVER_URL/api/collection-info/spot_photos" | jq '.' 2>/dev/null || curl -X GET "$SERVER_URL/api/collection-info/spot_photos"
echo ""

echo "=== 测试完成 ==="
echo ""
echo "如果需要创建 spot_likes 集合，请运行："
echo "curl -X POST $SERVER_URL/api/create-spot-likes-collection"
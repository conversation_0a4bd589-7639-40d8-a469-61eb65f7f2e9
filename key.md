fishing-app 读写操作 key 
权限：
允许读取、写入和列出特定存储桶中的对象。

存储桶：
fishing-app
使用此令牌对 Cloudflare API 进行身份验证：
令牌值
****************************************

为 S3 客户端使用以下凭据：
访问密钥 ID
2975b9f0b5ed91f29bec884c3fdcd4c8

机密访问密钥
c4a9d142e0c21f02e4d88caaf996d2f5a13c6372dbd2862c94ea845265cb3424

为 S3 客户端使用管辖权地特定的终结点：
https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com


########################################

fishing-app 只读 Token 
权限：
允许读取和列出特定存储桶中的对象。

存储桶：
fishing-app
使用此令牌对 Cloudflare API 进行身份验证：
令牌值
****************************************

为 S3 客户端使用以下凭据：
访问密钥 ID
9ce7c7d0cde4eedeee735ddc2dc6a225

机密访问密钥
c7082b2c7efea648e866ab8bc2620bb58a376ae1f6826f39ca2f252078de3958

为 S3 客户端使用管辖权地特定的终结点：
https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com


#######################################

管理员 令牌
权限：
允许创建、列出和删除存储桶，编辑存储桶配置，读取、写入和列出对象，以及对数据目录表和相关元数据进行读取和写入访问。

存储桶：
此帐户上的所有 R2 存储桶
使用此令牌对 Cloudflare API 进行身份验证：
令牌值
****************************************

为 S3 客户端使用以下凭据：
访问密钥 ID
ade7e701765b4ea99c1fabac282b56cc

机密访问密钥
bd4f3c8f3bb432b13e92492a6120ea49bb6268b23d6a3b82b446f43d7cd60bcf

为 S3 客户端使用管辖权地特定的终结点：
https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com